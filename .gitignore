# ===================================================================
# JetBrains Rider & ReSharper
#
# Specific files and directories created by Rider IDE.
# ===================================================================
# Rider project settings and caches
.idea/
*.sln.DotSettings
*.suo
_ReSharper.*/
# User-specific files
*.user
*.userosscache
*.sln.docstates

# =omer================================================================
# .NET & Build Artifacts
#
# General files generated by the .NET SDK, MSBuild, and compilers.
# ===================================================================
# Build output directories
[Bb]in/
[Oo]bj/

# NuGet packages directory
# It's better to restore packages than to commit them.
[Pp]ackages/
project.lock.json
project.assets.json

# Visual Studio / .NET files
.vs/
*.nupkg
*.nuget.props
*.nuget.targets

# ===================================================================
# Avalonia UI Specific
#
# Files and directories specific to Avalonia projects.
# ===================================================================
# Avalonia XAML compilation cache
obj/Avalonia/
obj/Debug/net*/**/*.g.cs
obj/Release/net*/**/*.g.cs

# Avalonia previewer files (if any)
.avalonia/

# ===================================================================
# OS & General Junk
#
# Common files generated by operating systems that should be ignored.
# ===================================================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*
.Spotlight-V100
.Trashes

# Linux
*~
.nfs*

# ===================================================================
# Secrets & Environment Files
#
# It is critical to never commit secrets or environment-specific files.
# ===================================================================
# Common naming conventions for local environment files
.env
appsettings.Development.json
secrets.json

# Example for a specific file you might create
local.settings.json

# ===================================================================
# Test Results & Logs
#
# Files generated by test runners and logging frameworks.
# ===================================================================
# Test results
TestResult.xml
*.trx
*.testresults
coverage.cobertura.xml
*.coverlet.json

# Logs
*.log
logs/
HRPayrollSystem/Services/ConnectionInfo.cs
