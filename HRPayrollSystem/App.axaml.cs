using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using HRPayrollSystem.ViewModels;
using HRPayrollSystem.Views;
using System;
using System.Text;
using HRPayrollSystem.Services;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using Microsoft.Extensions.DependencyInjection;
using SkiaSharp;
using ServiceProvider = HRPayrollSystem.Services.ServiceProvider;

namespace HRPayrollSystem;

public partial class App : Application
{
    private IServiceProvider? _serviceProvider;
    
    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
        
        // 配置依赖注入
        var services = new ServiceCollection();
        services.AddApplicationServices();
        
        _serviceProvider = services.BuildServiceProvider();
        ServiceProvider.Initialize(_serviceProvider);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        LiveCharts.Configure(config =>
        {
            // 方式 A：直接匹配系统已安装的“微软雅黑”
            //config.HasGlobalSKTypeface(SKTypeface.FromFamilyName("Microsoft YaHei"));

            // 方式 B：如果系统字体不确定，用 MatchCharacter 自动找能渲染“汉”的字体
             config.HasGlobalSKTypeface(SKFontManager.Default.MatchCharacter('汉'));
        });
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            var userManagementService = _serviceProvider?.GetRequiredService<UserManagementService>()
                ?? throw new InvalidOperationException("UserManagementService not found in DI container");
            
            desktop.MainWindow = new LoginWindow
            {
                DataContext = new LoginWindowViewModel(userManagementService),
            };
        }

        base.OnFrameworkInitializationCompleted();
        Console.OutputEncoding = Encoding.UTF8;
    }
}