using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;

namespace HRPayrollSystem.Services
{
    public class TransferService
    {
        private readonly EmployeeManagementService _employeeService;

        public TransferService(EmployeeManagementService employeeService)
        {
            _employeeService = employeeService;
        }

        // 创建员工调动记录
        public async Task<bool> CreateTransferAsync(TransferLog transfer)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                // 开启事务
                await using var transaction = await connection.BeginTransactionAsync();

                try
                {
                    // 1. 添加调动记录
                    const string transferSql = @"
                        INSERT INTO t_transfer_log(
                            employee_id, transfer_date, type, 
                            old_department_id, new_department_id, 
                            old_position_id, new_position_id, 
                            reason)
                        VALUES (
                            @employeeId, @transferDate, @type, 
                            @oldDepartmentId, @newDepartmentId, 
                            @oldPositionId, @newPositionId, 
                            @reason)";

                    await using var cmd = new MySqlCommand(transferSql, connection, transaction);
                    cmd.Parameters.AddWithValue("@employeeId", transfer.EmployeeId);
                    cmd.Parameters.AddWithValue("@transferDate", transfer.TransferDate);
                    cmd.Parameters.AddWithValue("@type", transfer.Type);
                    cmd.Parameters.AddWithValue("@oldDepartmentId", transfer.OldDepartmentId ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@newDepartmentId", transfer.NewDepartmentId ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@oldPositionId", transfer.OldPositionId ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@newPositionId", transfer.NewPositionId ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@reason", transfer.Reason ?? (object)DBNull.Value);

                    await cmd.ExecuteNonQueryAsync();

                    // 2. 更新员工信息
                    string updateSql = "UPDATE t_employee SET";
                    List<string> updateFields = new List<string>();
                    
                    if (transfer.Type == "部门调动" && transfer.NewDepartmentId.HasValue)
                    {
                        updateFields.Add(" department_id = @newDepartmentId");
                    }
                    else if (transfer.Type == "职位调动" && transfer.NewPositionId.HasValue)
                    {
                        updateFields.Add(" position_id = @newPositionId");
                    }
                    else if (transfer.Type == "部门与职位调动")
                    {
                        if (transfer.NewDepartmentId.HasValue)
                        {
                            updateFields.Add(" department_id = @newDepartmentId");
                        }
                        if (transfer.NewPositionId.HasValue)
                        {
                            updateFields.Add(" position_id = @newPositionId");
                        }
                    }
                    
                    if (updateFields.Count > 0)
                    {
                        updateSql += string.Join(", ", updateFields);
                        updateSql += ", updated_at = NOW() WHERE id = @employeeId";
                        
                        await using var updateCmd = new MySqlCommand(updateSql, connection, transaction);
                        updateCmd.Parameters.AddWithValue("@employeeId", transfer.EmployeeId);
                        
                        if ((transfer.Type == "部门调动" || transfer.Type == "部门与职位调动") && transfer.NewDepartmentId.HasValue)
                        {
                            updateCmd.Parameters.AddWithValue("@newDepartmentId", transfer.NewDepartmentId.Value);
                        }
                        
                        if ((transfer.Type == "职位调动" || transfer.Type == "部门与职位调动") && transfer.NewPositionId.HasValue)
                        {
                            updateCmd.Parameters.AddWithValue("@newPositionId", transfer.NewPositionId.Value);
                        }
                        
                        await updateCmd.ExecuteNonQueryAsync();
                    }

                    // 提交事务
                    await transaction.CommitAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    Console.WriteLine($"创建员工调动记录时发生错误: {ex.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建员工调动记录时发生错误: {ex.Message}");
                return false;
            }
        }

        // 获取员工的调动历史
        public async Task<List<TransferLog>> GetEmployeeTransferHistoryAsync(int employeeId)
        {
            var transfers = new List<TransferLog>();
            
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    SELECT t.*, 
                           e.name as employee_name, e.employee_number,
                           od.name as old_department_name, nd.name as new_department_name,
                           op.name as old_position_name, np.name as new_position_name
                    FROM t_transfer_log t
                    LEFT JOIN t_employee e ON t.employee_id = e.id
                    LEFT JOIN t_department od ON t.old_department_id = od.id
                    LEFT JOIN t_department nd ON t.new_department_id = nd.id
                    LEFT JOIN t_position op ON t.old_position_id = op.id
                    LEFT JOIN t_position np ON t.new_position_id = np.id
                    WHERE t.employee_id = @employeeId AND t.is_deleted = FALSE
                    ORDER BY t.transfer_date DESC";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@employeeId", employeeId);

                await using var reader = await cmd.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    transfers.Add(new TransferLog
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        TransferDate = reader.GetDateTime("transfer_date"),
                        Type = reader.GetString("type"),
                        OldDepartmentId = reader.IsDBNull(reader.GetOrdinal("old_department_id")) ? null : (int?)reader.GetInt32("old_department_id"),
                        NewDepartmentId = reader.IsDBNull(reader.GetOrdinal("new_department_id")) ? null : (int?)reader.GetInt32("new_department_id"),
                        OldDepartmentName = reader.IsDBNull(reader.GetOrdinal("old_department_name")) ? null : reader.GetString("old_department_name"),
                        NewDepartmentName = reader.IsDBNull(reader.GetOrdinal("new_department_name")) ? null : reader.GetString("new_department_name"),
                        OldPositionId = reader.IsDBNull(reader.GetOrdinal("old_position_id")) ? null : (int?)reader.GetInt32("old_position_id"),
                        NewPositionId = reader.IsDBNull(reader.GetOrdinal("new_position_id")) ? null : (int?)reader.GetInt32("new_position_id"),
                        OldPositionName = reader.IsDBNull(reader.GetOrdinal("old_position_name")) ? null : reader.GetString("old_position_name"),
                        NewPositionName = reader.IsDBNull(reader.GetOrdinal("new_position_name")) ? null : reader.GetString("new_position_name"),
                        Reason = reader.IsDBNull(reader.GetOrdinal("reason")) ? null : reader.GetString("reason"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取员工调动历史时发生错误: {ex.Message}");
            }
            
            return transfers;
        }
    }
} 