using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;

namespace HRPayrollSystem.Services;

public class DepartmentManagementService
{
    public async Task<bool> DeleteDepartmentAsync(int id)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               UPDATE t_department
                               SET is_deleted = TRUE, updated_at = NOW()
                               WHERE id = @id
                               """;
            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@id", id);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"删除部门记录时发生错误: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> InsertDepartmentAsync(Department department)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               INSERT INTO t_department (name, parent_id) 
                               VALUES (@name, @parent_id)
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@name", department.Name);
            cmd.Parameters.AddWithValue("@parent_id", department.ParentId);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"插入部门记录时发生错误: {ex.Message}");
            return false;
        }
    }
    
    public async Task<List<Department>> FindAllAsync()
    {
        var departments = new List<Department>();

        await using var connection = new MySqlConnection(ConnectionString);
        await connection.OpenAsync();

        const string sql = """
            SELECT d.id, d.name, d.parent_id, d.created_at, d.updated_at,
                   CASE 
                       WHEN d.parent_id = 0 THEN '无'
                       ELSE COALESCE(p.name, '未知部门')
                   END as parent_name
            FROM t_department d
            LEFT JOIN t_department p ON d.parent_id = p.id AND p.is_deleted = FALSE
            WHERE d.is_deleted = FALSE
            ORDER BY d.parent_id, d.id
            """;
        await using var cmd = new MySqlCommand(sql, connection);

        await using var reader = await cmd.ExecuteReaderAsync();

        int idOrdinal = reader.GetOrdinal("id");
        int nameOrdinal = reader.GetOrdinal("name");
        int parentIdOrdinal = reader.GetOrdinal("parent_id");
        int parentNameOrdinal = reader.GetOrdinal("parent_name");
        int createdAtOrdinal = reader.GetOrdinal("created_at");
        int updatedAtOrdinal = reader.GetOrdinal("updated_at");

        while (await reader.ReadAsync())
        {
            var department = new Department
            {
                Id = reader.GetInt32(idOrdinal),
                Name = reader.GetString(nameOrdinal),
                ParentId = reader.GetInt32(parentIdOrdinal),
                ParentName = reader.GetString(parentNameOrdinal),
                CreatedAt = reader.GetDateTime(createdAtOrdinal),
                UpdatedAt = reader.GetDateTime(updatedAtOrdinal)
            };
            Console.WriteLine($"Department: {department.Name}, Parent: {department.ParentName}");
            departments.Add(department);
        }

        return departments;
    }

    public async Task<bool> UpdateDepartmentAsync(Department department)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               UPDATE t_department
                               SET name = @name, parent_id = @parent_id, updated_at = NOW()
                               WHERE id = @id
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@id", department.Id);
            cmd.Parameters.AddWithValue("@name", department.Name);
            cmd.Parameters.AddWithValue("@parent_id", department.ParentId);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新部门记录时发生错误: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> ValidateParentDepartmentAsync(int parentId)
    {
        if (parentId == 0) return true; // 0表示董事部，总是有效

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = "SELECT COUNT(*) FROM t_department WHERE id = @parent_id AND is_deleted = FALSE";
            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@parent_id", parentId);

            var count = await cmd.ExecuteScalarAsync();
            return Convert.ToInt32(count) > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"验证上级部门时发生错误: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> ValidateDepartmentLevelAsync(int departmentId, int parentId)
    {
        if (parentId == 0) return true; // 董事部总是可以作为上级部门

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            // 获取当前部门的层级
            var currentLevel = await GetDepartmentLevelAsync(departmentId);
            // 获取上级部门的层级
            var parentLevel = await GetDepartmentLevelAsync(parentId);

            // 上级部门层级必须比当前部门层级高一级
            return parentLevel == currentLevel - 1;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"验证部门层级时发生错误: {ex.Message}");
            return false;
        }
    }

    public async Task<int> GetDepartmentLevelAsync(int departmentId)
    {
        if (departmentId == 0) return 0; // 董事部是0级

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                WITH RECURSIVE dept_hierarchy AS (
                    SELECT id, parent_id, 0 as level
                    FROM t_department 
                    WHERE parent_id = 0 AND is_deleted = FALSE
                    
                    UNION ALL
                    
                    SELECT d.id, d.parent_id, dh.level + 1
                    FROM t_department d
                    INNER JOIN dept_hierarchy dh ON d.parent_id = dh.id
                    WHERE d.is_deleted = FALSE
                )
                SELECT level FROM dept_hierarchy WHERE id = @department_id
                """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@department_id", departmentId);

            var result = await cmd.ExecuteScalarAsync();
            return result != null ? Convert.ToInt32(result) : 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取部门层级时发生错误: {ex.Message}");
            return 0;
        }
    }

    public async Task<List<Department>> SearchDepartmentsAsync(string searchTerm)
    {
        var departments = new List<Department>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                SELECT d.id, d.name, d.parent_id, d.created_at, d.updated_at,
                       CASE 
                           WHEN d.parent_id = 0 THEN '董事部'
                           ELSE COALESCE(p.name, '未知部门')
                       END as parent_name
                FROM t_department d
                LEFT JOIN t_department p ON d.parent_id = p.id AND p.is_deleted = FALSE
                WHERE d.is_deleted = FALSE AND d.name LIKE @search_term
                ORDER BY d.parent_id, d.id
                """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@search_term", $"%{searchTerm}%");

            await using var reader = await cmd.ExecuteReaderAsync();

            int idOrdinal = reader.GetOrdinal("id");
            int nameOrdinal = reader.GetOrdinal("name");
            int parentIdOrdinal = reader.GetOrdinal("parent_id");
            int parentNameOrdinal = reader.GetOrdinal("parent_name");
            int createdAtOrdinal = reader.GetOrdinal("created_at");
            int updatedAtOrdinal = reader.GetOrdinal("updated_at");

            while (await reader.ReadAsync())
            {
                var department = new Department
                {
                    Id = reader.GetInt32(idOrdinal),
                    Name = reader.GetString(nameOrdinal),
                    ParentId = reader.GetInt32(parentIdOrdinal),
                    ParentName = reader.GetString(parentNameOrdinal),
                    CreatedAt = reader.GetDateTime(createdAtOrdinal),
                    UpdatedAt = reader.GetDateTime(updatedAtOrdinal)
                };
                departments.Add(department);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"搜索部门时发生错误: {ex.Message}");
        }

        return departments;
    }

    public async Task<List<Department>> GetAllDepartmentsForSelectionAsync()
    {
        var departments = new List<Department>();

        await using var connection = new MySqlConnection(ConnectionString);
        await connection.OpenAsync();

        const string sql = """
            SELECT d.id, d.name, d.parent_id,
                   CASE 
                       WHEN d.parent_id = 0 THEN '董事部'
                       ELSE COALESCE(p.name, '未知部门')
                   END as parent_name
            FROM t_department d
            LEFT JOIN t_department p ON d.parent_id = p.id AND p.is_deleted = FALSE
            WHERE d.is_deleted = FALSE
            ORDER BY d.parent_id, d.id
            """;
        await using var cmd = new MySqlCommand(sql, connection);

        await using var reader = await cmd.ExecuteReaderAsync();

        int idOrdinal = reader.GetOrdinal("id");
        int nameOrdinal = reader.GetOrdinal("name");
        int parentIdOrdinal = reader.GetOrdinal("parent_id");
        int parentNameOrdinal = reader.GetOrdinal("parent_name");

        while (await reader.ReadAsync())
        {
            var department = new Department
            {
                Id = reader.GetInt32(idOrdinal),
                Name = reader.GetString(nameOrdinal),
                ParentId = reader.GetInt32(parentIdOrdinal),
                ParentName = reader.GetString(parentNameOrdinal)
            };
            departments.Add(department);
        }

        return departments;
    }
} 