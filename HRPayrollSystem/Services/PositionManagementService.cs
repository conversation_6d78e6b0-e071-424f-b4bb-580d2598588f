using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;

namespace HRPayrollSystem.Services;

public class PositionManagementService
{
    public async Task<bool> DeletePositionAsync(int id)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               UPDATE t_position
                               SET is_deleted = TRUE, updated_at = NOW()
                               WHERE id = @id
                               """;
            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@id", id);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"删除岗位记录时发生错误: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> InsertPositionAsync(Position position)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               INSERT INTO t_position (name, description) 
                               VALUES (@name, @description)
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@name", position.Name);
            cmd.Parameters.AddWithValue("@description", position.Description);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"插入岗位记录时发生错误: {ex.Message}");
            return false;
        }
    }
    
    public async Task<List<Position>> FindAllAsync()
    {
        var positions = new List<Position>();

        await using var connection = new MySqlConnection(ConnectionString);
        await connection.OpenAsync();

        const string sql =
            "SELECT id, name, description, created_at, updated_at FROM t_position WHERE is_deleted = FALSE";
        await using var cmd = new MySqlCommand(sql, connection);

        await using var reader = await cmd.ExecuteReaderAsync();

        int idOrdinal = reader.GetOrdinal("id");
        int nameOrdinal = reader.GetOrdinal("name");
        int descriptionOrdinal = reader.GetOrdinal("description");
        int createdAtOrdinal = reader.GetOrdinal("created_at");
        int updatedAtOrdinal = reader.GetOrdinal("updated_at");

        while (await reader.ReadAsync())
        {
            var position = new Position
            {
                Id = reader.GetInt32(idOrdinal),
                Name = reader.GetString(nameOrdinal),
                Description = reader.IsDBNull(descriptionOrdinal) ? null : reader.GetString(descriptionOrdinal),
                CreatedAt = reader.GetDateTime(createdAtOrdinal),
                UpdatedAt = reader.GetDateTime(updatedAtOrdinal)
            };
            Console.WriteLine($"Position: {position.Name}, Description: {position.Description}");
            positions.Add(position);
        }

        return positions;
    }

    public async Task<bool> UpdatePositionAsync(Position position)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               UPDATE t_position
                               SET name = @name, description = @description, updated_at = NOW()
                               WHERE id = @id
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@id", position.Id);
            cmd.Parameters.AddWithValue("@name", position.Name);
            cmd.Parameters.AddWithValue("@description", position.Description);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新岗位记录时发生错误: {ex.Message}");
            return false;
        }
    }

    public async Task<List<Position>> SearchPositionsAsync(string searchTerm)
    {
        var positions = new List<Position>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = @"SELECT id, name, description, created_at, updated_at FROM t_position WHERE is_deleted = FALSE AND name LIKE @search_term ORDER BY id";
            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@search_term", $"%{searchTerm}%");

            await using var reader = await cmd.ExecuteReaderAsync();

            int idOrdinal = reader.GetOrdinal("id");
            int nameOrdinal = reader.GetOrdinal("name");
            int descriptionOrdinal = reader.GetOrdinal("description");
            int createdAtOrdinal = reader.GetOrdinal("created_at");
            int updatedAtOrdinal = reader.GetOrdinal("updated_at");

            while (await reader.ReadAsync())
            {
                var position = new Position
                {
                    Id = reader.GetInt32(idOrdinal),
                    Name = reader.GetString(nameOrdinal),
                    Description = reader.IsDBNull(descriptionOrdinal) ? null : reader.GetString(descriptionOrdinal),
                    CreatedAt = reader.GetDateTime(createdAtOrdinal),
                    UpdatedAt = reader.GetDateTime(updatedAtOrdinal)
                };
                positions.Add(position);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"搜索岗位时发生错误: {ex.Message}");
        }

        return positions;
    }
} 