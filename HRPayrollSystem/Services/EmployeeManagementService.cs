﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;

namespace HRPayrollSystem.Services;

public class EmployeeManagementService
{
    public async Task<IEnumerable<string>> SearchTop10EmployeeNumbersAndNamesAsync(string searchText)
    {
        var results = new List<string>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync(); // Open() 的异步版本

            const string sql = """
                               SELECT id, employee_number, name, CONCAT(employee_number, ' ', name) as to_search 
                               FROM t_employee 
                               WHERE CONCAT(employee_number, ' ', name) LIKE @searchText
                               LIMIT 10
                               """;
        
            await using var cmd = new MySqlCommand(sql, connection);

            cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");

            // 使用 ExecuteReaderAsync() 的异步版本
            await using var reader = await cmd.ExecuteReaderAsync();

            // 异步读取每一行

            while (await reader.ReadAsync())
            {
                Console.WriteLine($"Employee: {reader.GetString("to_search")}");
                results.Add(reader.GetString("to_search"));
            }


            return results;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"搜索员工编号和姓名时发生错误: {ex.Message}");
        }

        return results;
    }
    // 定义事件，当员工信息（特别是基本工资）更新时触发
    public event EventHandler<EventArgs>? EmployeeUpdated;
    
    // 触发EmployeeUpdated事件的方法
    protected virtual void OnEmployeeUpdated()
    {
        EmployeeUpdated?.Invoke(this, EventArgs.Empty);
    }
    
    // 插入新员工记录
    public async Task<bool> InsertEmployeeAsync(Employee employee)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               INSERT INTO t_employee (
                                   employee_number, name, gender, id_card, birthday,
                                   phone, email, address, department_id, position_id,
                                   hire_date, probation_end_date, status, base_salary,
                                   resignation_date, resignation_reason
                               ) VALUES (
                                   @employeeNumber, @name, @gender, @idCard, @birthday,
                                   @phone, @email, @address, @departmentId, @positionId,
                                   @hireDate, @probationEndDate, @status, @baseSalary,
                                   @resignationDate, @resignationReason
                               )
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@employeeNumber", employee.EmployeeNumber);
            cmd.Parameters.AddWithValue("@name", employee.Name);
            cmd.Parameters.AddWithValue("@gender", employee.Gender ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@idCard", employee.IdCard ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@birthday", employee.Birthday ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@phone", employee.Phone ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@email", employee.Email ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@address", employee.Address ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@departmentId", employee.DepartmentId ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@positionId", employee.PositionId ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@hireDate", employee.HireDate ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@probationEndDate", employee.ProbationEndDate ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@status", employee.Status ?? "在职");
            cmd.Parameters.AddWithValue("@baseSalary", employee.BaseSalary);
            cmd.Parameters.AddWithValue("@resignationDate", employee.ResignationDate ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@resignationReason", employee.ResignationReason ?? (object)DBNull.Value);

            var result = await cmd.ExecuteNonQueryAsync();
            
            if (result > 0)
            {
                // 触发员工更新事件
                OnEmployeeUpdated();
            }
            
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"插入员工记录时发生错误: {ex.Message}");
            return false;
        }
    }

    // 删除员工记录
    public async Task<bool> DeleteEmployeeAsync(int id)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            // 使用事务确保所有操作都成功
            await using var transaction = await connection.BeginTransactionAsync();
            
            try
            {
                // 1. 软删除员工记录
                const string deleteEmployeeSql = """
                                   UPDATE t_employee
                                   SET is_deleted = TRUE, updated_at = NOW()
                                   WHERE id = @id
                                   """;
                await using var cmd1 = new MySqlCommand(deleteEmployeeSql, connection, transaction);
                cmd1.Parameters.AddWithValue("@id", id);
                var result1 = await cmd1.ExecuteNonQueryAsync();

                if (result1 > 0)
                {
                    // 2. 软删除该员工的奖罚记录
                    const string deleteRewardPunishmentSql = """
                                       UPDATE t_reward_penalty
                                       SET is_deleted = TRUE, updated_at = NOW()
                                       WHERE employee_id = @employeeId
                                       """;
                    await using var cmd2 = new MySqlCommand(deleteRewardPunishmentSql, connection, transaction);
                    cmd2.Parameters.AddWithValue("@employeeId", id);
                    await cmd2.ExecuteNonQueryAsync();

                    // 3. 软删除该员工的考勤记录
                    const string deleteAttendanceSql = """
                                       UPDATE t_attendance
                                       SET is_deleted = TRUE, updated_at = NOW()
                                       WHERE employee_id = @employeeId
                                       """;
                    await using var cmd3 = new MySqlCommand(deleteAttendanceSql, connection, transaction);
                    cmd3.Parameters.AddWithValue("@employeeId", id);
                    await cmd3.ExecuteNonQueryAsync();

                    // 4. 软删除该员工的工资记录
                    const string deletePayrollSql = """
                                       UPDATE t_payroll
                                       SET is_deleted = TRUE, updated_at = NOW()
                                       WHERE employee_id = @employeeId
                                       """;
                    await using var cmd4 = new MySqlCommand(deletePayrollSql, connection, transaction);
                    cmd4.Parameters.AddWithValue("@employeeId", id);
                    await cmd4.ExecuteNonQueryAsync();

                    // 提交事务
                    await transaction.CommitAsync();
                    
                    // 触发员工更新事件
                    OnEmployeeUpdated();
                    
                    return true;
                }
                else
                {
                    await transaction.RollbackAsync();
                    return false;
                }
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"删除员工记录时发生错误: {ex.Message}");
            return false;
        }
    }

    // 更新员工记录
    public async Task<bool> UpdateEmployeeAsync(Employee employee)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               UPDATE t_employee
                               SET name = @name,
                                   gender = @gender,
                                   id_card = @idCard,
                                   birthday = @birthday,
                                   phone = @phone,
                                   email = @email,
                                   address = @address,
                                   department_id = @departmentId,
                                   position_id = @positionId,
                                   hire_date = @hireDate,
                                   probation_end_date = @probationEndDate,
                                   status = @status,
                                   base_salary = @baseSalary,
                                   resignation_date = @resignationDate,
                                   resignation_reason = @resignationReason,
                                   updated_at = NOW()
                               WHERE id = @id
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@id", employee.Id);
            cmd.Parameters.AddWithValue("@name", employee.Name);
            cmd.Parameters.AddWithValue("@gender", employee.Gender ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@idCard", employee.IdCard ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@birthday", employee.Birthday ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@phone", employee.Phone ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@email", employee.Email ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@address", employee.Address ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@departmentId", employee.DepartmentId ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@positionId", employee.PositionId ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@hireDate", employee.HireDate ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@probationEndDate", employee.ProbationEndDate ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@status", employee.Status);
            cmd.Parameters.AddWithValue("@baseSalary", employee.BaseSalary);
            cmd.Parameters.AddWithValue("@resignationDate", employee.ResignationDate ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@resignationReason", employee.ResignationReason ?? (object)DBNull.Value);

            var result = await cmd.ExecuteNonQueryAsync();
            
            if (result > 0)
            {
                // 触发员工更新事件
                OnEmployeeUpdated();
            }
            
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新员工记录时发生错误: {ex.Message}");
            return false;
        }
    }

    // 根据ID查找员工
    public async Task<Employee?> FindByIdAsync(int id)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT e.id, e.employee_number, e.name, e.gender, e.id_card, e.birthday,
                                      e.phone, e.email, e.address, e.department_id, d.name as department_name,
                                      e.position_id, p.name as position_name, e.hire_date, e.probation_end_date,
                                      e.status, e.base_salary, e.resignation_date, e.resignation_reason,
                                      e.created_at, e.updated_at
                               FROM t_employee e
                               LEFT JOIN t_department d ON e.department_id = d.id
                               LEFT JOIN t_position p ON e.position_id = p.id
                               WHERE e.id = @id AND e.is_deleted = FALSE
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@id", id);

            await using var reader = await cmd.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                // 获取所有列的索引
                int idIndex = reader.GetOrdinal("id");
                int empNumIndex = reader.GetOrdinal("employee_number");
                int nameIndex = reader.GetOrdinal("name");
                int genderIndex = reader.GetOrdinal("gender");
                int idCardIndex = reader.GetOrdinal("id_card");
                int birthdayIndex = reader.GetOrdinal("birthday");
                int phoneIndex = reader.GetOrdinal("phone");
                int emailIndex = reader.GetOrdinal("email");
                int addressIndex = reader.GetOrdinal("address");
                int deptIdIndex = reader.GetOrdinal("department_id");
                int deptNameIndex = reader.GetOrdinal("department_name");
                int positionIdIndex = reader.GetOrdinal("position_id");
                int positionNameIndex = reader.GetOrdinal("position_name");
                int hireDateIndex = reader.GetOrdinal("hire_date");
                int probationEndDateIndex = reader.GetOrdinal("probation_end_date");
                int statusIndex = reader.GetOrdinal("status");
                int baseSalaryIndex = reader.GetOrdinal("base_salary");
                int resignationDateIndex = reader.GetOrdinal("resignation_date");
                int resignationReasonIndex = reader.GetOrdinal("resignation_reason");
                int createdAtIndex = reader.GetOrdinal("created_at");
                int updatedAtIndex = reader.GetOrdinal("updated_at");

                return new Employee
                {
                    Id = reader.GetInt32(idIndex),
                    EmployeeNumber = reader.GetString(empNumIndex),
                    Name = reader.GetString(nameIndex),
                    Gender = reader.IsDBNull(genderIndex) ? null : reader.GetString(genderIndex),
                    IdCard = reader.IsDBNull(idCardIndex) ? null : reader.GetString(idCardIndex),
                    Birthday = reader.IsDBNull(birthdayIndex) ? null : reader.GetDateTime(birthdayIndex),
                    Phone = reader.IsDBNull(phoneIndex) ? null : reader.GetString(phoneIndex),
                    Email = reader.IsDBNull(emailIndex) ? null : reader.GetString(emailIndex),
                    Address = reader.IsDBNull(addressIndex) ? null : reader.GetString(addressIndex),
                    DepartmentId = reader.IsDBNull(deptIdIndex) ? null : (int?)reader.GetInt32(deptIdIndex),
                    Department = reader.IsDBNull(deptNameIndex) ? null : reader.GetString(deptNameIndex),
                    PositionId = reader.IsDBNull(positionIdIndex) ? null : (int?)reader.GetInt32(positionIdIndex),
                    Position = reader.IsDBNull(positionNameIndex) ? null : reader.GetString(positionNameIndex),
                    HireDate = reader.IsDBNull(hireDateIndex) ? null : reader.GetDateTime(hireDateIndex),
                    ProbationEndDate = reader.IsDBNull(probationEndDateIndex)
                        ? null
                        : reader.GetDateTime(probationEndDateIndex),
                    Status = reader.IsDBNull(statusIndex) ? null : reader.GetString(statusIndex),
                    BaseSalary = reader.GetDecimal(baseSalaryIndex),
                    ResignationDate = reader.IsDBNull(resignationDateIndex)
                        ? null
                        : reader.GetDateTime(resignationDateIndex),
                    ResignationReason = reader.IsDBNull(resignationReasonIndex)
                        ? null
                        : reader.GetString(resignationReasonIndex),
                    CreatedAt = reader.GetDateTime(createdAtIndex),
                    UpdatedAt = reader.GetDateTime(updatedAtIndex)
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"查找员工记录时发生错误: {ex.Message}");
            return null;
        }
    }

    // 查找所有员工
    public async Task<List<Employee>> FindAllAsync()
    {
        var employees = new List<Employee>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT e.id, e.employee_number, e.name, e.gender, e.id_card, e.birthday,
                                      e.phone, e.email, e.address, e.department_id, d.name as department_name,
                                      e.position_id, p.name as position_name, e.hire_date, e.probation_end_date,
                                      e.status, e.base_salary, e.resignation_date, e.resignation_reason,
                                      e.created_at, e.updated_at
                               FROM t_employee e
                               LEFT JOIN t_department d ON e.department_id = d.id
                               LEFT JOIN t_position p ON e.position_id = p.id
                               WHERE e.is_deleted = FALSE
                               ORDER BY e.created_at DESC
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            await using var reader = await cmd.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                // 先获取所有列的索引
                int idIndex = reader.GetOrdinal("id");
                int empNumIndex = reader.GetOrdinal("employee_number");
                int nameIndex = reader.GetOrdinal("name");
                int genderIndex = reader.GetOrdinal("gender");
                int idCardIndex = reader.GetOrdinal("id_card");
                int birthdayIndex = reader.GetOrdinal("birthday");
                int phoneIndex = reader.GetOrdinal("phone");
                int emailIndex = reader.GetOrdinal("email");
                int addressIndex = reader.GetOrdinal("address");
                int deptIdIndex = reader.GetOrdinal("department_id");
                int deptNameIndex = reader.GetOrdinal("department_name");
                int positionIdIndex = reader.GetOrdinal("position_id");
                int positionNameIndex = reader.GetOrdinal("position_name");
                int hireDateIndex = reader.GetOrdinal("hire_date");
                int probationEndDateIndex = reader.GetOrdinal("probation_end_date");
                int statusIndex = reader.GetOrdinal("status");
                int baseSalaryIndex = reader.GetOrdinal("base_salary");
                int resignationDateIndex = reader.GetOrdinal("resignation_date");
                int resignationReasonIndex = reader.GetOrdinal("resignation_reason");
                int createdAtIndex = reader.GetOrdinal("created_at");
                int updatedAtIndex = reader.GetOrdinal("updated_at");

                var employee = new Employee
                {
                    Id = reader.GetInt32(idIndex),
                    EmployeeNumber = reader.GetString(empNumIndex),
                    Name = reader.GetString(nameIndex),
                    Gender = reader.IsDBNull(genderIndex) ? null : reader.GetString(genderIndex),
                    IdCard = reader.IsDBNull(idCardIndex) ? null : reader.GetString(idCardIndex),
                    Birthday = reader.IsDBNull(birthdayIndex) ? null : reader.GetDateTime(birthdayIndex),
                    Phone = reader.IsDBNull(phoneIndex) ? null : reader.GetString(phoneIndex),
                    Email = reader.IsDBNull(emailIndex) ? null : reader.GetString(emailIndex),
                    Address = reader.IsDBNull(addressIndex) ? null : reader.GetString(addressIndex),
                    DepartmentId = reader.IsDBNull(deptIdIndex) ? null : (int?)reader.GetInt32(deptIdIndex),
                    Department = reader.IsDBNull(deptNameIndex) ? null : reader.GetString(deptNameIndex),
                    PositionId = reader.IsDBNull(positionIdIndex) ? null : (int?)reader.GetInt32(positionIdIndex),
                    Position = reader.IsDBNull(positionNameIndex) ? null : reader.GetString(positionNameIndex),
                    HireDate = reader.IsDBNull(hireDateIndex) ? null : reader.GetDateTime(hireDateIndex),
                    ProbationEndDate = reader.IsDBNull(probationEndDateIndex)
                        ? null
                        : reader.GetDateTime(probationEndDateIndex),
                    Status = reader.IsDBNull(statusIndex) ? null : reader.GetString(statusIndex),
                    BaseSalary = reader.GetDecimal(baseSalaryIndex),
                    ResignationDate = reader.IsDBNull(resignationDateIndex)
                        ? null
                        : reader.GetDateTime(resignationDateIndex),
                    ResignationReason = reader.IsDBNull(resignationReasonIndex)
                        ? null
                        : reader.GetString(resignationReasonIndex),
                    CreatedAt = reader.GetDateTime(createdAtIndex),
                    UpdatedAt = reader.GetDateTime(updatedAtIndex)
                };
                employees.Add(employee);
            }

            return employees;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"查找所有员工记录时发生错误: {ex.Message}");
            return employees;
        }
    }

    public async Task<List<Employee>> SearchEmployeesAsync(string searchText)
    {
        var employees = new List<Employee>();
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT e.id, e.employee_number, e.name, e.gender, e.id_card, e.birthday,
                                      e.phone, e.email, e.address, e.department_id, d.name as department_name,
                                      e.position_id, p.name as position_name, e.hire_date, e.probation_end_date,
                                      e.status, e.base_salary, e.resignation_date, e.resignation_reason,
                                      e.created_at, e.updated_at
                               FROM t_employee e
                               LEFT JOIN t_department d ON e.department_id = d.id
                               LEFT JOIN t_position p ON e.position_id = p.id
                               WHERE e.is_deleted = FALSE AND (e.name LIKE @searchText OR e.employee_number LIKE @searchText)
                               ORDER BY e.created_at DESC
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");

            await using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                employees.Add(new Employee
                {
                    Id = reader.GetInt32(reader.GetOrdinal("id")),
                    EmployeeNumber = reader.GetString(reader.GetOrdinal("employee_number")),
                    Name = reader.GetString(reader.GetOrdinal("name")),
                    Department = reader.IsDBNull(reader.GetOrdinal("department_name"))
                        ? ""
                        : reader.GetString(reader.GetOrdinal("department_name")),
                    Position = reader.IsDBNull(reader.GetOrdinal("position_name"))
                        ? ""
                        : reader.GetString(reader.GetOrdinal("position_name")),
                    HireDate = reader.IsDBNull(reader.GetOrdinal("hire_date"))
                        ? (DateTime?)null
                        : reader.GetDateTime(reader.GetOrdinal("hire_date")),
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"搜索员工时发生错误: {ex.Message}");
        }

        return employees;
    }

    public async Task<List<Department>> GetAllDepartmentsAsync()
    {
        var departments = new List<Department>();
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();
            const string sql = "SELECT id, name FROM t_department WHERE is_deleted = FALSE";
            await using var cmd = new MySqlCommand(sql, connection);
            await using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                departments.Add(new Department
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1)
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取部门列表时发生错误: {ex.Message}");
        }

        return departments;
    }

    public async Task<List<Position>> GetAllPositionsAsync()
    {
        var positions = new List<Position>();
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();
            const string sql = "SELECT id, name FROM t_position WHERE is_deleted = FALSE";
            await using var cmd = new MySqlCommand(sql, connection);
            await using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                positions.Add(new Position
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1)
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取职位列表时发生错误: {ex.Message}");
        }

        return positions;
    }

    // 根据员工编号查找员工
    public async Task<Employee?> FindByEmployeeNumberAsync(string employeeNumber)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT e.id, e.employee_number, e.name, e.gender, e.id_card, e.birthday,
                                      e.phone, e.email, e.address, e.department_id, d.name as department_name,
                                      e.position_id, p.name as position_name, e.hire_date, e.probation_end_date,
                                      e.status, e.base_salary, e.resignation_date, e.resignation_reason,
                                      e.created_at, e.updated_at
                               FROM t_employee e
                               LEFT JOIN t_department d ON e.department_id = d.id AND d.is_deleted = FALSE
                               LEFT JOIN t_position p ON e.position_id = p.id AND p.is_deleted = FALSE
                               WHERE e.employee_number = @employeeNumber AND e.is_deleted = FALSE
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@employeeNumber", employeeNumber);

            await using var reader = await cmd.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                // 获取所有列的索引
                int idIndex = reader.GetOrdinal("id");
                int empNumIndex = reader.GetOrdinal("employee_number");
                int nameIndex = reader.GetOrdinal("name");
                int genderIndex = reader.GetOrdinal("gender");
                int idCardIndex = reader.GetOrdinal("id_card");
                int birthdayIndex = reader.GetOrdinal("birthday");
                int phoneIndex = reader.GetOrdinal("phone");
                int emailIndex = reader.GetOrdinal("email");
                int addressIndex = reader.GetOrdinal("address");
                int deptIdIndex = reader.GetOrdinal("department_id");
                int deptNameIndex = reader.GetOrdinal("department_name");
                int positionIdIndex = reader.GetOrdinal("position_id");
                int positionNameIndex = reader.GetOrdinal("position_name");
                int hireDateIndex = reader.GetOrdinal("hire_date");
                int probationEndDateIndex = reader.GetOrdinal("probation_end_date");
                int statusIndex = reader.GetOrdinal("status");
                int baseSalaryIndex = reader.GetOrdinal("base_salary");
                int resignationDateIndex = reader.GetOrdinal("resignation_date");
                int resignationReasonIndex = reader.GetOrdinal("resignation_reason");
                int createdAtIndex = reader.GetOrdinal("created_at");
                int updatedAtIndex = reader.GetOrdinal("updated_at");

                return new Employee
                {
                    Id = reader.GetInt32(idIndex),
                    EmployeeNumber = reader.GetString(empNumIndex),
                    Name = reader.GetString(nameIndex),
                    Gender = reader.IsDBNull(genderIndex) ? null : reader.GetString(genderIndex),
                    IdCard = reader.IsDBNull(idCardIndex) ? null : reader.GetString(idCardIndex),
                    Birthday = reader.IsDBNull(birthdayIndex) ? null : reader.GetDateTime(birthdayIndex),
                    Phone = reader.IsDBNull(phoneIndex) ? null : reader.GetString(phoneIndex),
                    Email = reader.IsDBNull(emailIndex) ? null : reader.GetString(emailIndex),
                    Address = reader.IsDBNull(addressIndex) ? null : reader.GetString(addressIndex),
                    DepartmentId = reader.IsDBNull(deptIdIndex) ? null : (int?)reader.GetInt32(deptIdIndex),
                    Department = reader.IsDBNull(deptNameIndex) ? null : reader.GetString(deptNameIndex),
                    PositionId = reader.IsDBNull(positionIdIndex) ? null : (int?)reader.GetInt32(positionIdIndex),
                    Position = reader.IsDBNull(positionNameIndex) ? null : reader.GetString(positionNameIndex),
                    HireDate = reader.IsDBNull(hireDateIndex) ? null : reader.GetDateTime(hireDateIndex),
                    ProbationEndDate = reader.IsDBNull(probationEndDateIndex)
                        ? null
                        : reader.GetDateTime(probationEndDateIndex),
                    Status = reader.IsDBNull(statusIndex) ? null : reader.GetString(statusIndex),
                    BaseSalary = reader.GetDecimal(baseSalaryIndex),
                    ResignationDate = reader.IsDBNull(resignationDateIndex)
                        ? null
                        : reader.GetDateTime(resignationDateIndex),
                    ResignationReason = reader.IsDBNull(resignationReasonIndex)
                        ? null
                        : reader.GetString(resignationReasonIndex),
                    CreatedAt = reader.GetDateTime(createdAtIndex),
                    UpdatedAt = reader.GetDateTime(updatedAtIndex)
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"根据工号查找员工记录时发生错误: {ex.Message}");
            return null;
        }
    }

    // 根据姓名查找员工
    public async Task<Employee?> FindByNameAsync(string name)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT e.id, e.employee_number, e.name, e.gender, e.id_card, e.birthday,
                                      e.phone, e.email, e.address, e.department_id, d.name as department_name,
                                      e.position_id, p.name as position_name, e.hire_date, e.probation_end_date,
                                      e.status, e.base_salary, e.resignation_date, e.resignation_reason,
                                      e.created_at, e.updated_at
                               FROM t_employee e
                               LEFT JOIN t_department d ON e.department_id = d.id
                               LEFT JOIN t_position p ON e.position_id = p.id
                               WHERE e.name = @name AND e.is_deleted = FALSE
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@name", name);

            await using var reader = await cmd.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                // 获取所有列的索引
                int idIndex = reader.GetOrdinal("id");
                int empNumIndex = reader.GetOrdinal("employee_number");
                int nameIndex = reader.GetOrdinal("name");
                int genderIndex = reader.GetOrdinal("gender");
                int idCardIndex = reader.GetOrdinal("id_card");
                int birthdayIndex = reader.GetOrdinal("birthday");
                int phoneIndex = reader.GetOrdinal("phone");
                int emailIndex = reader.GetOrdinal("email");
                int addressIndex = reader.GetOrdinal("address");
                int deptIdIndex = reader.GetOrdinal("department_id");
                int deptNameIndex = reader.GetOrdinal("department_name");
                int positionIdIndex = reader.GetOrdinal("position_id");
                int positionNameIndex = reader.GetOrdinal("position_name");
                int hireDateIndex = reader.GetOrdinal("hire_date");
                int probationEndDateIndex = reader.GetOrdinal("probation_end_date");
                int statusIndex = reader.GetOrdinal("status");
                int baseSalaryIndex = reader.GetOrdinal("base_salary");
                int resignationDateIndex = reader.GetOrdinal("resignation_date");
                int resignationReasonIndex = reader.GetOrdinal("resignation_reason");
                int createdAtIndex = reader.GetOrdinal("created_at");
                int updatedAtIndex = reader.GetOrdinal("updated_at");

                return new Employee
                {
                    Id = reader.GetInt32(idIndex),
                    EmployeeNumber = reader.GetString(empNumIndex),
                    Name = reader.GetString(nameIndex),
                    Gender = reader.IsDBNull(genderIndex) ? null : reader.GetString(genderIndex),
                    IdCard = reader.IsDBNull(idCardIndex) ? null : reader.GetString(idCardIndex),
                    Birthday = reader.IsDBNull(birthdayIndex) ? null : reader.GetDateTime(birthdayIndex),
                    Phone = reader.IsDBNull(phoneIndex) ? null : reader.GetString(phoneIndex),
                    Email = reader.IsDBNull(emailIndex) ? null : reader.GetString(emailIndex),
                    Address = reader.IsDBNull(addressIndex) ? null : reader.GetString(addressIndex),
                    DepartmentId = reader.IsDBNull(deptIdIndex) ? null : (int?)reader.GetInt32(deptIdIndex),
                    Department = reader.IsDBNull(deptNameIndex) ? null : reader.GetString(deptNameIndex),
                    PositionId = reader.IsDBNull(positionIdIndex) ? null : (int?)reader.GetInt32(positionIdIndex),
                    Position = reader.IsDBNull(positionNameIndex) ? null : reader.GetString(positionNameIndex),
                    HireDate = reader.IsDBNull(hireDateIndex) ? null : reader.GetDateTime(hireDateIndex),
                    ProbationEndDate = reader.IsDBNull(probationEndDateIndex)
                        ? null
                        : reader.GetDateTime(probationEndDateIndex),
                    Status = reader.IsDBNull(statusIndex) ? null : reader.GetString(statusIndex),
                    BaseSalary = reader.GetDecimal(baseSalaryIndex),
                    ResignationDate = reader.IsDBNull(resignationDateIndex)
                        ? null
                        : reader.GetDateTime(resignationDateIndex),
                    ResignationReason = reader.IsDBNull(resignationReasonIndex)
                        ? null
                        : reader.GetString(resignationReasonIndex),
                    CreatedAt = reader.GetDateTime(createdAtIndex),
                    UpdatedAt = reader.GetDateTime(updatedAtIndex)
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"根据姓名查找员工记录时发生错误: {ex.Message}");
            return null;
        }
    }

    // 根据部门查找员工
    public async Task<List<Employee>> FindByDepartmentAsync(int departmentId)
    {
        var employees = new List<Employee>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT e.id, e.employee_number, e.name, e.gender, e.id_card, e.birthday,
                                      e.phone, e.email, e.address, e.department_id, d.name as department_name,
                                      e.position_id, p.name as position_name, e.hire_date, e.probation_end_date,
                                      e.status, e.base_salary, e.resignation_date, e.resignation_reason,
                                      e.created_at, e.updated_at
                               FROM t_employee e
                               LEFT JOIN t_department d ON e.department_id = d.id
                               LEFT JOIN t_position p ON e.position_id = p.id
                               WHERE e.department_id = @departmentId AND e.is_deleted = FALSE
                               ORDER BY e.created_at DESC
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@departmentId", departmentId);
            await using var reader = await cmd.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                // 先获取所有列的索引
                int idIndex = reader.GetOrdinal("id");
                int empNumIndex = reader.GetOrdinal("employee_number");
                int nameIndex = reader.GetOrdinal("name");
                int genderIndex = reader.GetOrdinal("gender");
                int idCardIndex = reader.GetOrdinal("id_card");
                int birthdayIndex = reader.GetOrdinal("birthday");
                int phoneIndex = reader.GetOrdinal("phone");
                int emailIndex = reader.GetOrdinal("email");
                int addressIndex = reader.GetOrdinal("address");
                int deptIdIndex = reader.GetOrdinal("department_id");
                int deptNameIndex = reader.GetOrdinal("department_name");
                int positionIdIndex = reader.GetOrdinal("position_id");
                int positionNameIndex = reader.GetOrdinal("position_name");
                int hireDateIndex = reader.GetOrdinal("hire_date");
                int probationEndDateIndex = reader.GetOrdinal("probation_end_date");
                int statusIndex = reader.GetOrdinal("status");
                int baseSalaryIndex = reader.GetOrdinal("base_salary");
                int resignationDateIndex = reader.GetOrdinal("resignation_date");
                int resignationReasonIndex = reader.GetOrdinal("resignation_reason");
                int createdAtIndex = reader.GetOrdinal("created_at");
                int updatedAtIndex = reader.GetOrdinal("updated_at");

                var employee = new Employee
                {
                    Id = reader.GetInt32(idIndex),
                    EmployeeNumber = reader.GetString(empNumIndex),
                    Name = reader.GetString(nameIndex),
                    Gender = reader.IsDBNull(genderIndex) ? null : reader.GetString(genderIndex),
                    IdCard = reader.IsDBNull(idCardIndex) ? null : reader.GetString(idCardIndex),
                    Birthday = reader.IsDBNull(birthdayIndex) ? null : reader.GetDateTime(birthdayIndex),
                    Phone = reader.IsDBNull(phoneIndex) ? null : reader.GetString(phoneIndex),
                    Email = reader.IsDBNull(emailIndex) ? null : reader.GetString(emailIndex),
                    Address = reader.IsDBNull(addressIndex) ? null : reader.GetString(addressIndex),
                    DepartmentId = reader.IsDBNull(deptIdIndex) ? null : (int?)reader.GetInt32(deptIdIndex),
                    Department = reader.IsDBNull(deptNameIndex) ? null : reader.GetString(deptNameIndex),
                    PositionId = reader.IsDBNull(positionIdIndex) ? null : (int?)reader.GetInt32(positionIdIndex),
                    Position = reader.IsDBNull(positionNameIndex) ? null : reader.GetString(positionNameIndex),
                    HireDate = reader.IsDBNull(hireDateIndex) ? null : reader.GetDateTime(hireDateIndex),
                    ProbationEndDate = reader.IsDBNull(probationEndDateIndex)
                        ? null
                        : reader.GetDateTime(probationEndDateIndex),
                    Status = reader.IsDBNull(statusIndex) ? null : reader.GetString(statusIndex),
                    BaseSalary = reader.GetDecimal(baseSalaryIndex),
                    ResignationDate = reader.IsDBNull(resignationDateIndex)
                        ? null
                        : reader.GetDateTime(resignationDateIndex),
                    ResignationReason = reader.IsDBNull(resignationReasonIndex)
                        ? null
                        : reader.GetString(resignationReasonIndex),
                    CreatedAt = reader.GetDateTime(createdAtIndex),
                    UpdatedAt = reader.GetDateTime(updatedAtIndex)
                };
            }

            return employees;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"根据部门查找员工记录时发生错误: {ex.Message}");
            return employees;
        }
    }

    // 初始化基础数据
    public async Task InitializeBasicDataAsync()
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            // 检查是否已有部门数据
            const string checkDeptSql = "SELECT COUNT(*) FROM t_department WHERE is_deleted = FALSE";
            await using var checkDeptCmd = new MySqlCommand(checkDeptSql, connection);
            var deptCount = Convert.ToInt32(await checkDeptCmd.ExecuteScalarAsync());

            if (deptCount == 0)
            {
                // 插入基础部门数据
                const string insertDeptSql = """
                                             INSERT INTO t_department (name, parent_id) VALUES 
                                             ('技术部', 0),
                                             ('人事部', 0),
                                             ('财务部', 0),
                                             ('市场部', 0),
                                             ('运营部', 0)
                                             """;
                await using var insertDeptCmd = new MySqlCommand(insertDeptSql, connection);
                await insertDeptCmd.ExecuteNonQueryAsync();
            }

            // 检查是否已有职位数据
            const string checkPosSql = "SELECT COUNT(*) FROM t_position WHERE is_deleted = FALSE";
            await using var checkPosCmd = new MySqlCommand(checkPosSql, connection);
            var posCount = Convert.ToInt32(await checkPosCmd.ExecuteScalarAsync());

            if (posCount == 0)
            {
                // 插入基础职位数据
                const string insertPosSql = """
                                            INSERT INTO t_position (name, description) VALUES 
                                            ('软件工程师', '负责软件开发工作'),
                                            ('人事专员', '负责人事管理工作'),
                                            ('财务专员', '负责财务管理工作'),
                                            ('市场专员', '负责市场推广工作'),
                                            ('运营专员', '负责运营管理工作'),
                                            ('部门经理', '负责部门管理工作'),
                                            ('总经理', '负责公司整体管理工作')
                                            """;
                await using var insertPosCmd = new MySqlCommand(insertPosSql, connection);
                await insertPosCmd.ExecuteNonQueryAsync();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"初始化基础数据时发生错误: {ex.Message}");
        }
    }
}