using Microsoft.Extensions.DependencyInjection;
using System;

namespace HRPayrollSystem.Services
{
    public static class ServiceProvider
    {
        private static IServiceProvider? _serviceProvider;
        
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }
        
        public static T GetService<T>() where T : class
        {
            if (_serviceProvider == null)
                throw new InvalidOperationException("ServiceProvider has not been initialized.");
                
            return _serviceProvider.GetRequiredService<T>();
        }
        
        public static T? GetOptionalService<T>() where T : class
        {
            if (_serviceProvider == null)
                throw new InvalidOperationException("ServiceProvider has not been initialized.");
                
            return _serviceProvider.GetService<T>();
        }
        
        public static IServiceProvider Current => _serviceProvider ?? 
            throw new InvalidOperationException("ServiceProvider has not been initialized.");
    }
}
