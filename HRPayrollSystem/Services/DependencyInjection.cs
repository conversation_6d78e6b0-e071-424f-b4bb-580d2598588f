using Microsoft.Extensions.DependencyInjection;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels;
using HRPayrollSystem.ViewModels.Pages;
using HRPayrollSystem.ViewModels.Dialogs;

namespace HRPayrollSystem.Services
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // 注册基础服务 (单例)
            services.AddSingleton<EmployeeManagementService>();
            services.AddSingleton<DepartmentManagementService>();
            services.AddSingleton<PositionManagementService>();
            services.AddSingleton<UserManagementService>();
            services.AddSingleton<RewardPunishmentManagementService>();
            
            // 注册有依赖的服务 (单例)
            services.AddSingleton<TransferService>();
            services.AddSingleton<AttendanceManagementService>();
            services.AddSingleton<PayrollManagementService>();
            
            return services;
        }
    }
}
