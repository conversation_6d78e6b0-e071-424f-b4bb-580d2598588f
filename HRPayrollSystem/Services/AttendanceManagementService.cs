using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;
using System.Data;
using ClosedXML.Excel;
using System.Linq;

namespace HRPayrollSystem.Services
{
    public class AttendanceManagementService
    {
        private readonly EmployeeManagementService _employeeService;
        
        // 定义事件，当考勤记录变更时触发
        public event EventHandler<EventArgs>? AttendanceChanged;
        
        // 触发AttendanceChanged事件的方法
        protected virtual void OnAttendanceChanged()
        {
            AttendanceChanged?.Invoke(this, EventArgs.Empty);
        }

        public AttendanceManagementService(EmployeeManagementService employeeService)
        {
            _employeeService = employeeService;
        }
        
        // 根据员工ID和月份查找考勤记录
        public async Task<List<Attendance>> FindByEmployeeAndMonthAsync(int employeeId, int year, int month)
        {
            var attendances = new List<Attendance>();
            try
            {
                // 获取员工信息以检查入职日期
                var employee = await _employeeService.FindByIdAsync(employeeId);
                if (employee == null)
                {
                    Console.WriteLine($"无法查找考勤记录：找不到ID为 {employeeId} 的员工");
                    return attendances;
                }
                
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                string sql;
                MySqlCommand cmd;
                
                // 如果员工有入职日期，确保只返回入职日期之后的考勤记录
                if (employee.HireDate.HasValue)
                {
                    sql = @"
                        SELECT a.*, e.name as employee_name, e.employee_number,
                               d.name as department_name, p.name as position_name
                        FROM t_attendance a
                        LEFT JOIN t_employee e ON a.employee_id = e.id
                        LEFT JOIN t_department d ON e.department_id = d.id
                        LEFT JOIN t_position p ON e.position_id = p.id
                        WHERE a.employee_id = @employeeId 
                              AND YEAR(a.date) = @year 
                              AND MONTH(a.date) = @month 
                              AND a.date >= @hireDate
                              AND a.is_deleted = FALSE
                        ORDER BY a.date";

                    cmd = new MySqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@employeeId", employeeId);
                    cmd.Parameters.AddWithValue("@year", year);
                    cmd.Parameters.AddWithValue("@month", month);
                    cmd.Parameters.AddWithValue("@hireDate", employee.HireDate.Value);
                }
                else
                {
                    // 如果员工没有入职日期，使用原始查询
                    sql = @"
                        SELECT a.*, e.name as employee_name, e.employee_number,
                               d.name as department_name, p.name as position_name
                        FROM t_attendance a
                        LEFT JOIN t_employee e ON a.employee_id = e.id
                        LEFT JOIN t_department d ON e.department_id = d.id
                        LEFT JOIN t_position p ON e.position_id = p.id
                        WHERE a.employee_id = @employeeId 
                              AND YEAR(a.date) = @year 
                              AND MONTH(a.date) = @month 
                              AND a.is_deleted = FALSE
                        ORDER BY a.date";

                    cmd = new MySqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@employeeId", employeeId);
                    cmd.Parameters.AddWithValue("@year", year);
                    cmd.Parameters.AddWithValue("@month", month);
                }

                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    attendances.Add(new Attendance
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? null : reader.GetString("department_name"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? null : reader.GetString("position_name"),
                        Date = reader.GetDateTime("date"),
                        Status = reader.GetString("status"),
                        Notes = reader.IsDBNull(reader.GetOrdinal("notes")) ? null : reader.GetString("notes"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据员工和月份查找考勤记录时发生错误: {ex.Message}");
            }
            return attendances;
        }

        // 根据员工ID和日期查找考勤记录
        public async Task<Attendance?> FindByEmployeeAndDateAsync(int employeeId, DateTime date)
        {
            try
            {
                // 获取员工信息以检查入职日期
                var employee = await _employeeService.FindByIdAsync(employeeId);
                if (employee == null)
                {
                    Console.WriteLine($"无法查找考勤记录：找不到ID为 {employeeId} 的员工");
                    return null;
                }
                
                // 验证查询日期是否在员工入职日期之后
                if (employee.HireDate.HasValue && date.Date < employee.HireDate.Value.Date)
                {
                    Console.WriteLine($"无法查询考勤记录：查询日期 {date:yyyy-MM-dd} 早于员工入职日期 {employee.HireDate.Value:yyyy-MM-dd}");
                    return null;
                }
                
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    SELECT a.*, e.name as employee_name, e.employee_number,
                           d.name as department_name, p.name as position_name
                    FROM t_attendance a
                    LEFT JOIN t_employee e ON a.employee_id = e.id
                    LEFT JOIN t_department d ON e.department_id = d.id
                    LEFT JOIN t_position p ON e.position_id = p.id
                    WHERE a.employee_id = @employeeId AND a.date = @date AND a.is_deleted = FALSE";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@employeeId", employeeId);
                cmd.Parameters.AddWithValue("@date", date.Date);

                await using var reader = await cmd.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new Attendance
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? null : reader.GetString("department_name"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? null : reader.GetString("position_name"),
                        Date = reader.GetDateTime("date"),
                        Status = reader.GetString("status"),
                        Notes = reader.IsDBNull(reader.GetOrdinal("notes")) ? null : reader.GetString("notes"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据员工和日期查找考勤记录时发生错误: {ex.Message}");
                return null;
            }
        }

        // 添加考勤记录
        public async Task<bool> InsertAttendanceAsync(Attendance attendance)
        {
            try
            {
                // 获取员工信息以检查入职日期
                var employee = await _employeeService.FindByIdAsync(attendance.EmployeeId);
                if (employee == null)
                {
                    Console.WriteLine($"无法添加考勤记录：找不到ID为 {attendance.EmployeeId} 的员工");
                    return false;
                }
                
                // 验证考勤日期是否在员工入职日期之后
                if (employee.HireDate.HasValue && attendance.Date.Date < employee.HireDate.Value.Date)
                {
                    Console.WriteLine($"无法添加考勤记录：考勤日期 {attendance.Date:yyyy-MM-dd} 早于员工入职日期 {employee.HireDate.Value:yyyy-MM-dd}");
                    return false;
                }

                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    INSERT INTO t_attendance(employee_id, date, status, notes)
                    VALUES(@employeeId, @date, @status, @notes)";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@employeeId", attendance.EmployeeId);
                cmd.Parameters.AddWithValue("@date", attendance.Date);
                cmd.Parameters.AddWithValue("@status", attendance.Status);
                cmd.Parameters.AddWithValue("@notes", attendance.Notes ?? (object)DBNull.Value);

                var result = await cmd.ExecuteNonQueryAsync();
                
                if (result > 0)
                {
                    // 触发考勤变更事件
                    OnAttendanceChanged();
                }
                
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加考勤记录时发生错误: {ex.Message}");
                return false;
            }
        }

        // 更新考勤记录
        public async Task<bool> UpdateAttendanceAsync(Attendance attendance)
        {
            try
            {
                // 获取员工信息以检查入职日期
                var employee = await _employeeService.FindByIdAsync(attendance.EmployeeId);
                if (employee == null)
                {
                    Console.WriteLine($"无法更新考勤记录：找不到ID为 {attendance.EmployeeId} 的员工");
                    return false;
                }
                
                // 验证考勤日期是否在员工入职日期之后
                if (employee.HireDate.HasValue && attendance.Date.Date < employee.HireDate.Value.Date)
                {
                    Console.WriteLine($"无法更新考勤记录：考勤日期 {attendance.Date:yyyy-MM-dd} 早于员工入职日期 {employee.HireDate.Value:yyyy-MM-dd}");
                    return false;
                }
                
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    UPDATE t_attendance
                    SET status = @status, notes = @notes, updated_at = NOW()
                    WHERE id = @id";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@id", attendance.Id);
                cmd.Parameters.AddWithValue("@status", attendance.Status);
                cmd.Parameters.AddWithValue("@notes", attendance.Notes ?? (object)DBNull.Value);

                var result = await cmd.ExecuteNonQueryAsync();
                
                if (result > 0)
                {
                    // 触发考勤变更事件
                    OnAttendanceChanged();
                }
                
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新考勤记录时发生错误: {ex.Message}");
                return false;
            }
        }

        // 删除考勤记录
        public async Task<bool> DeleteAttendanceAsync(int id)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    UPDATE t_attendance
                    SET is_deleted = TRUE, updated_at = NOW()
                    WHERE id = @id";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@id", id);

                var result = await cmd.ExecuteNonQueryAsync();
                
                if (result > 0)
                {
                    // 触发考勤变更事件
                    OnAttendanceChanged();
                }
                
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除考勤记录时发生错误: {ex.Message}");
                return false;
            }
        }

        // 查找所有考勤记录
        public async Task<List<Attendance>> FindAllAsync()
        {
            var attendances = new List<Attendance>();
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    SELECT a.*, e.name as employee_name, e.employee_number,
                           d.name as department_name, p.name as position_name
                    FROM t_attendance a
                    LEFT JOIN t_employee e ON a.employee_id = e.id
                    LEFT JOIN t_department d ON e.department_id = d.id
                    LEFT JOIN t_position p ON e.position_id = p.id
                    WHERE a.is_deleted = FALSE
                    ORDER BY a.date DESC, a.employee_id";

                await using var cmd = new MySqlCommand(sql, connection);
                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    attendances.Add(new Attendance
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? null : reader.GetString("department_name"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? null : reader.GetString("position_name"),
                        Date = reader.GetDateTime("date"),
                        Status = reader.GetString("status"),
                        Notes = reader.IsDBNull(reader.GetOrdinal("notes")) ? null : reader.GetString("notes"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查找所有考勤记录时发生错误: {ex.Message}");
            }
            return attendances;
        }

        // 按员工搜索考勤记录
        public async Task<List<Attendance>> SearchAttendanceAsync(string searchText, DateTime? startDate = null, DateTime? endDate = null)
        {
            var attendances = new List<Attendance>();
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                string sql = @"
                    SELECT a.*, e.name as employee_name, e.employee_number,
                           d.name as department_name, p.name as position_name
                    FROM t_attendance a
                    LEFT JOIN t_employee e ON a.employee_id = e.id
                    LEFT JOIN t_department d ON e.department_id = d.id
                    LEFT JOIN t_position p ON e.position_id = p.id
                    WHERE a.is_deleted = FALSE AND 
                          (e.name LIKE @searchText OR e.employee_number LIKE @searchText
                           OR d.name LIKE @searchText OR p.name LIKE @searchText)";
                          
                if (startDate.HasValue)
                {
                    sql += " AND a.date >= @startDate";
                }
                
                if (endDate.HasValue)
                {
                    sql += " AND a.date <= @endDate";
                }
                
                sql += " ORDER BY a.date DESC, a.employee_id";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");
                
                if (startDate.HasValue)
                {
                    cmd.Parameters.AddWithValue("@startDate", startDate.Value.Date);
                }
                
                if (endDate.HasValue)
                {
                    cmd.Parameters.AddWithValue("@endDate", endDate.Value.Date);
                }

                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    attendances.Add(new Attendance
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? null : reader.GetString("department_name"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? null : reader.GetString("position_name"),
                        Date = reader.GetDateTime("date"),
                        Status = reader.GetString("status"),
                        Notes = reader.IsDBNull(reader.GetOrdinal("notes")) ? null : reader.GetString("notes"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"搜索考勤记录时发生错误: {ex.Message}");
            }
            return attendances;
        }
        
        // 导出考勤记录到Excel
        public async Task<string> ExportAttendanceToExcelAsync(DateTime startDate, DateTime endDate, int? departmentId = null)
        {
            try
            {
                // 获取考勤数据
                var summaries = await FindByDateRangeAsync(startDate, endDate, departmentId);
                if (summaries.Count == 0)
                {
                    return string.Empty;
                }
                
                // 创建Excel工作簿
                using var workbook = new ClosedXML.Excel.XLWorkbook();
                var worksheet = workbook.Worksheets.Add("考勤记录");
                
                // 设置标题行
                worksheet.Cell(1, 1).Value = "工号";
                worksheet.Cell(1, 2).Value = "姓名";
                worksheet.Cell(1, 3).Value = "部门";
                worksheet.Cell(1, 4).Value = "职位";
                worksheet.Cell(1, 5).Value = "正常出勤";
                worksheet.Cell(1, 6).Value = "迟到次数";
                worksheet.Cell(1, 7).Value = "早退次数";
                worksheet.Cell(1, 8).Value = "旷工次数";
                worksheet.Cell(1, 9).Value = "请假次数";
                worksheet.Cell(1, 10).Value = "总考勤天数";
                
                // 设置标题行样式
                var headerRow = worksheet.Row(1);
                headerRow.Style.Font.Bold = true;
                headerRow.Style.Fill.BackgroundColor = ClosedXML.Excel.XLColor.LightGray;
                headerRow.Style.Alignment.Horizontal = ClosedXML.Excel.XLAlignmentHorizontalValues.Center;
                
                // 填充数据
                for (int i = 0; i < summaries.Count; i++)
                {
                    var row = i + 2; // 从第二行开始
                    var summary = summaries[i];
                    
                    worksheet.Cell(row, 1).Value = summary.EmployeeNumber;
                    worksheet.Cell(row, 2).Value = summary.EmployeeName;
                    worksheet.Cell(row, 3).Value = summary.Department;
                    worksheet.Cell(row, 4).Value = summary.Position;
                    worksheet.Cell(row, 5).Value = summary.NormalCount;
                    worksheet.Cell(row, 6).Value = summary.LateCount;
                    worksheet.Cell(row, 7).Value = summary.EarlyLeaveCount;
                    worksheet.Cell(row, 8).Value = summary.AbsentCount;
                    worksheet.Cell(row, 9).Value = summary.LeaveCount;
                    
                    // 计算总考勤天数
                    int totalDays = summary.NormalCount + summary.LateCount + 
                                    summary.EarlyLeaveCount + summary.AbsentCount + 
                                    summary.LeaveCount;
                    worksheet.Cell(row, 10).Value = totalDays;
                }
                
                // 自动调整列宽
                worksheet.Columns().AdjustToContents();
                
                // 创建保存目录
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                var exportDir = Path.Combine(documentsPath, "HRPayrollSystem", "Exports");
                Directory.CreateDirectory(exportDir);
                
                // 生成文件名
                string dateRange = $"{startDate:yyyyMMdd}-{endDate:yyyyMMdd}";
                string fileName = $"考勤记录_{dateRange}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                string filePath = Path.Combine(exportDir, fileName);
                
                // 保存Excel文件
                workbook.SaveAs(filePath);
                
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出考勤记录到Excel时发生错误: {ex.Message}");
                return string.Empty;
            }
        }
        
        // 从Excel导入考勤记录
        public async Task<(int success, int failed, List<string> errors)> ImportAttendanceFromExcelAsync(string filePath)
        {
            int success = 0;
            int failed = 0;
            var errors = new List<string>();
            
            try
            {
                // 加载Excel文件
                using var workbook = new ClosedXML.Excel.XLWorkbook(filePath);
                var worksheet = workbook.Worksheets.First();
                
                // 获取表头行（假设第一行是表头）
                var headerRow = worksheet.Row(1);
                
                // 找到列索引
                int employeeNumberCol = -1;
                int dateCol = -1;
                int statusCol = -1;
                int notesCol = -1;
                
                for (int col = 1; col <= worksheet.LastColumnUsed().ColumnNumber(); col++)
                {
                    string header = worksheet.Cell(1, col).GetString();
                    
                    if (header.Contains("工号") || header.Contains("员工编号"))
                    {
                        employeeNumberCol = col;
                    }
                    else if (header.Contains("日期"))
                    {
                        dateCol = col;
                    }
                    else if (header.Contains("状态") || header.Contains("考勤状态"))
                    {
                        statusCol = col;
                    }
                    else if (header.Contains("备注") || header.Contains("说明"))
                    {
                        notesCol = col;
                    }
                }
                
                // 检查必需列是否存在
                if (employeeNumberCol == -1 || dateCol == -1 || statusCol == -1)
                {
                    errors.Add("Excel文件缺少必要的列：员工编号、日期、考勤状态");
                    return (0, 0, errors);
                }
                
                // 逐行处理数据，从第二行开始（跳过表头）
                for (int row = 2; row <= worksheet.LastRowUsed().RowNumber(); row++)
                {
                    try
                    {
                        // 读取单元格数据
                        string employeeNumber = worksheet.Cell(row, employeeNumberCol).GetString();
                        string dateStr = worksheet.Cell(row, dateCol).GetString();
                        string status = worksheet.Cell(row, statusCol).GetString();
                        string notes = notesCol != -1 ? worksheet.Cell(row, notesCol).GetString() : "";
                        
                        if (string.IsNullOrWhiteSpace(employeeNumber) || string.IsNullOrWhiteSpace(dateStr))
                        {
                            errors.Add($"第{row}行：员工编号或日期为空");
                            failed++;
                            continue;
                        }
                        
                        // 解析日期
                        if (!DateTime.TryParse(dateStr, out DateTime date))
                        {
                            errors.Add($"第{row}行：日期格式无效 - {dateStr}");
                            failed++;
                            continue;
                        }
                        
                        // 查找员工ID
                        var employee = await _employeeService.FindByEmployeeNumberAsync(employeeNumber);
                        if (employee == null)
                        {
                            errors.Add($"第{row}行：找不到员工编号为 {employeeNumber} 的员工");
                            failed++;
                            continue;
                        }
                        
                        // 验证状态值
                        if (status != "正常" && status != "迟到" && status != "早退" && status != "旷工" && status != "请假")
                        {
                            status = "正常"; // 默认为正常
                        }
                        
                        // 检查是否已存在考勤记录
                        var existingAttendance = await FindByEmployeeAndDateAsync(employee.Id, date);
                        if (existingAttendance != null)
                        {
                            // 更新现有记录
                            existingAttendance.Status = status;
                            existingAttendance.Notes = string.IsNullOrWhiteSpace(notes) ? null : notes;
                            
                            bool updated = await UpdateAttendanceAsync(existingAttendance);
                            if (updated)
                            {
                                success++;
                            }
                            else
                            {
                                errors.Add($"第{row}行：更新考勤记录失败 - 员工: {employee.Name}, 日期: {date:yyyy-MM-dd}");
                                failed++;
                            }
                        }
                        else
                        {
                            // 创建新记录
                            var attendance = new Attendance
                            {
                                EmployeeId = employee.Id,
                                Date = date,
                                Status = status,
                                Notes = string.IsNullOrWhiteSpace(notes) ? null : notes
                            };
                            
                            bool inserted = await InsertAttendanceAsync(attendance);
                            if (inserted)
                            {
                                success++;
                            }
                            else
                            {
                                errors.Add($"第{row}行：添加考勤记录失败 - 员工: {employee.Name}, 日期: {date:yyyy-MM-dd}");
                                failed++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"第{row}行：处理数据时发生错误 - {ex.Message}");
                        failed++;
                    }
                }
                
                return (success, failed, errors);
            }
            catch (Exception ex)
            {
                errors.Add($"导入考勤记录时发生错误: {ex.Message}");
                return (success, failed, errors);
            }
        }

        // 根据日期范围和部门查询考勤汇总
        public async Task<List<AttendanceSummary>> FindByDateRangeAsync(DateTime startDate, DateTime endDate, int? departmentId = null)
        {
            var summaries = new List<AttendanceSummary>();
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                var sql = @"
                    SELECT 
                        e.employee_number,
                        e.name as employee_name,
                        d.name as department,
                        p.name as position,
                        SUM(CASE WHEN a.status = '正常' THEN 1 ELSE 0 END) as normal_count,
                        SUM(CASE WHEN a.status = '迟到' THEN 1 ELSE 0 END) as late_count,
                        SUM(CASE WHEN a.status = '早退' THEN 1 ELSE 0 END) as early_leave_count,
                        SUM(CASE WHEN a.status = '旷工' THEN 1 ELSE 0 END) as absent_count,
                        SUM(CASE WHEN a.status = '请假' THEN 1 ELSE 0 END) as leave_count
                    FROM t_employee e
                    LEFT JOIN t_department d ON e.department_id = d.id
                    LEFT JOIN t_position p ON e.position_id = p.id
                    LEFT JOIN t_attendance a ON e.id = a.employee_id 
                        AND a.date BETWEEN @startDate AND @endDate
                        AND a.is_deleted = FALSE
                    WHERE e.is_deleted = FALSE";

                if (departmentId.HasValue)
                {
                    sql += " AND e.department_id = @departmentId";
                }

                sql += @" GROUP BY e.employee_number, e.name, d.name, p.name
                    ORDER BY d.name, e.employee_number";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@startDate", startDate);
                cmd.Parameters.AddWithValue("@endDate", endDate);
                if (departmentId.HasValue)
                {
                    cmd.Parameters.AddWithValue("@departmentId", departmentId.Value);
                }

                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    summaries.Add(new AttendanceSummary
                    {
                        EmployeeNumber = reader.GetString("employee_number"),
                        EmployeeName = reader.GetString("employee_name"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department")) ? "" : reader.GetString("department"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position")) ? "" : reader.GetString("position"),
                        NormalCount = reader.GetInt32("normal_count"),
                        LateCount = reader.GetInt32("late_count"),
                        EarlyLeaveCount = reader.GetInt32("early_leave_count"),
                        AbsentCount = reader.GetInt32("absent_count"),
                        LeaveCount = reader.GetInt32("leave_count")
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据日期范围查询考勤汇总时发生错误: {ex.Message}");
            }
            return summaries;
        }

        // 批量导入考勤记录
        public async Task<(int success, int failed)> ImportAttendanceFromDataTableAsync(DataTable dataTable)
        {
            int success = 0;
            int failed = 0;

            try
            {
                foreach (DataRow row in dataTable.Rows)
                {
                    try
                    {
                        string employeeNumber = row["员工编号"].ToString() ?? "";
                        string status = row["考勤状态"].ToString() ?? "正常";
                        string dateStr = row["日期"].ToString() ?? "";
                        string? notes = row["备注"]?.ToString();

                        // 查找员工
                        var employee = await _employeeService.FindByEmployeeNumberAsync(employeeNumber);
                        if (employee == null)
                        {
                            failed++;
                            continue;
                        }

                        // 解析日期
                        if (!DateTime.TryParse(dateStr, out DateTime attendanceDate))
                        {
                            failed++;
                            continue;
                        }

                        // 检查是否存在
                        var existingAttendance = await FindByEmployeeAndDateAsync(employee.Id, attendanceDate);
                        
                        // 创建考勤记录对象
                        var attendance = new Attendance
                        {
                            EmployeeId = employee.Id,
                            Date = attendanceDate,
                            Status = status,
                            Notes = notes
                        };

                        bool result;
                        if (existingAttendance != null)
                        {
                            // 更新已有记录
                            attendance.Id = existingAttendance.Id;
                            result = await UpdateAttendanceAsync(attendance);
                        }
                        else
                        {
                            // 添加新记录
                            result = await InsertAttendanceAsync(attendance);
                        }

                        if (result)
                            success++;
                        else
                            failed++;
                    }
                    catch
                    {
                        failed++;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量导入考勤记录时发生错误: {ex.Message}");
            }

            return (success, failed);
        }
    }
} 