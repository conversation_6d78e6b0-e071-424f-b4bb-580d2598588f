using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;

namespace HRPayrollSystem.Services
{
    public class RewardPunishmentManagementService
    {
        private readonly EmployeeManagementService _employeeService;
        
        // 定义奖惩记录变更事件
        public event EventHandler? RewardPunishmentChanged;
        
        // 构造函数，注入EmployeeManagementService
        public RewardPunishmentManagementService(EmployeeManagementService employeeService)
        {
            _employeeService = employeeService;
        }
        
        // 插入新奖罚记录
        public async Task<bool> InsertRewardPunishmentAsync(RewardPunishment rewardPunishment)
        {
            try
            {
                // 获取员工信息以检查入职日期
                var employee = await _employeeService.FindByIdAsync(rewardPunishment.EmployeeId);
                if (employee == null)
                {
                    Console.WriteLine($"无法添加奖惩记录：找不到ID为 {rewardPunishment.EmployeeId} 的员工");
                    return false;
                }
                
                // 验证奖惩记录日期是否在员工入职日期之后
                if (employee.HireDate.HasValue && rewardPunishment.RecordDate.Date < employee.HireDate.Value.Date)
                {
                    Console.WriteLine($"无法添加奖惩记录：记录日期 {rewardPunishment.RecordDate:yyyy-MM-dd} 早于员工入职日期 {employee.HireDate.Value:yyyy-MM-dd}");
                    return false;
                }
                
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               INSERT INTO t_reward_penalty (
                                   employee_id, type, amount, reason, record_date
                               ) VALUES (
                                   @employeeId, @type, @amount, @reason, @recordDate
                               )
                               """;

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@employeeId", rewardPunishment.EmployeeId);
                cmd.Parameters.AddWithValue("@type", rewardPunishment.Type);
                cmd.Parameters.AddWithValue("@amount", rewardPunishment.Amount);
                cmd.Parameters.AddWithValue("@reason", rewardPunishment.Reason ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@recordDate", rewardPunishment.RecordDate);

                var result = await cmd.ExecuteNonQueryAsync();
                
                // 如果插入成功，触发奖惩记录变更事件
                if (result > 0)
                {
                    RewardPunishmentChanged?.Invoke(this, EventArgs.Empty);
                }
                
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"插入奖罚记录时发生错误: {ex.Message}");
                return false;
            }
        }

        // 删除奖罚记录
        public async Task<bool> DeleteRewardPunishmentAsync(int id)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               UPDATE t_reward_penalty
                               SET is_deleted = TRUE, updated_at = NOW()
                               WHERE id = @id
                               """;
                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@id", id);

                var result = await cmd.ExecuteNonQueryAsync();
                
                // 如果删除成功，触发奖惩记录变更事件
                if (result > 0)
                {
                    RewardPunishmentChanged?.Invoke(this, EventArgs.Empty);
                }
                
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除奖罚记录时发生错误: {ex.Message}");
                return false;
            }
        }

        // 更新奖罚记录
        public async Task<bool> UpdateRewardPunishmentAsync(RewardPunishment rewardPunishment)
        {
            try
            {
                // 获取员工信息以检查入职日期
                var employee = await _employeeService.FindByIdAsync(rewardPunishment.EmployeeId);
                if (employee == null)
                {
                    Console.WriteLine($"无法更新奖惩记录：找不到ID为 {rewardPunishment.EmployeeId} 的员工");
                    return false;
                }
                
                // 验证奖惩记录日期是否在员工入职日期之后
                if (employee.HireDate.HasValue && rewardPunishment.RecordDate.Date < employee.HireDate.Value.Date)
                {
                    Console.WriteLine($"无法更新奖惩记录：记录日期 {rewardPunishment.RecordDate:yyyy-MM-dd} 早于员工入职日期 {employee.HireDate.Value:yyyy-MM-dd}");
                    return false;
                }
                
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               UPDATE t_reward_penalty
                               SET employee_id = @employeeId,
                                   type = @type,
                                   amount = @amount,
                                   reason = @reason,
                                   record_date = @recordDate,
                                   updated_at = NOW()
                               WHERE id = @id
                               """;

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@id", rewardPunishment.Id);
                cmd.Parameters.AddWithValue("@employeeId", rewardPunishment.EmployeeId);
                cmd.Parameters.AddWithValue("@type", rewardPunishment.Type);
                cmd.Parameters.AddWithValue("@amount", rewardPunishment.Amount);
                cmd.Parameters.AddWithValue("@reason", rewardPunishment.Reason ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@recordDate", rewardPunishment.RecordDate);

                var result = await cmd.ExecuteNonQueryAsync();
                
                // 如果更新成功，触发奖惩记录变更事件
                if (result > 0)
                {
                    RewardPunishmentChanged?.Invoke(this, EventArgs.Empty);
                }
                
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新奖罚记录时发生错误: {ex.Message}");
                return false;
            }
        }

        // 根据ID查找奖罚记录
        public async Task<RewardPunishment?> FindByIdAsync(int id)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               SELECT rp.id, rp.employee_id, e.name as employee_name, e.employee_number,
                                      rp.type, rp.amount, rp.reason, rp.record_date,
                                      rp.created_at, rp.updated_at
                               FROM t_reward_penalty rp
                               LEFT JOIN t_employee e ON rp.employee_id = e.id
                               WHERE rp.id = @id AND rp.is_deleted = FALSE
                               """;

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@id", id);

                await using var reader = await cmd.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new RewardPunishment
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        EmployeeId = reader.GetInt32(reader.GetOrdinal("employee_id")),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString(reader.GetOrdinal("employee_name")),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString(reader.GetOrdinal("employee_number")),
                        Type = reader.GetString(reader.GetOrdinal("type")),
                        Amount = reader.GetDecimal(reader.GetOrdinal("amount")),
                        Reason = reader.IsDBNull(reader.GetOrdinal("reason")) ? null : reader.GetString(reader.GetOrdinal("reason")),
                        RecordDate = reader.GetDateTime(reader.GetOrdinal("record_date")),
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查找奖罚记录时发生错误: {ex.Message}");
                return null;
            }
        }

        // 查找所有奖罚记录
        public async Task<List<RewardPunishment>> FindAllAsync()
        {
            var rewardPunishments = new List<RewardPunishment>();

            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               SELECT rp.id, rp.employee_id, e.name as employee_name, e.employee_number,
                                      rp.type, rp.amount, rp.reason, rp.record_date,
                                      rp.created_at, rp.updated_at, e.hire_date
                               FROM t_reward_penalty rp
                               LEFT JOIN t_employee e ON rp.employee_id = e.id
                               WHERE rp.is_deleted = FALSE
                               ORDER BY rp.created_at DESC
                               """;

                await using var cmd = new MySqlCommand(sql, connection);
                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    // 检查奖惩记录日期是否在员工入职日期之后
                    var hireDate = reader.IsDBNull(reader.GetOrdinal("hire_date")) ? 
                                  (DateTime?)null : reader.GetDateTime("hire_date");
                    var recordDate = reader.GetDateTime("record_date");
                    
                    if (hireDate.HasValue && recordDate < hireDate.Value)
                    {
                        // 跳过入职日期之前的记录
                        continue;
                    }
                    
                    rewardPunishments.Add(new RewardPunishment
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        EmployeeId = reader.GetInt32(reader.GetOrdinal("employee_id")),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString(reader.GetOrdinal("employee_name")),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString(reader.GetOrdinal("employee_number")),
                        Type = reader.GetString(reader.GetOrdinal("type")),
                        Amount = reader.GetDecimal(reader.GetOrdinal("amount")),
                        Reason = reader.IsDBNull(reader.GetOrdinal("reason")) ? null : reader.GetString(reader.GetOrdinal("reason")),
                        RecordDate = recordDate,
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查找所有奖罚记录时发生错误: {ex.Message}");
            }

            return rewardPunishments;
        }

        // 根据员工ID查找奖罚记录
        public async Task<List<RewardPunishment>> FindByEmployeeIdAsync(int employeeId)
        {
            var rewardPunishments = new List<RewardPunishment>();

            try
            {
                // 获取员工信息以检查入职日期
                var employee = await _employeeService.FindByIdAsync(employeeId);
                if (employee == null)
                {
                    Console.WriteLine($"无法查找奖惩记录：找不到ID为 {employeeId} 的员工");
                    return rewardPunishments;
                }
                
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                string sql;
                MySqlCommand cmd;
                
                // 如果员工有入职日期，确保只返回入职日期之后的奖惩记录
                if (employee.HireDate.HasValue)
                {
                    sql = """
                               SELECT rp.id, rp.employee_id, e.name as employee_name, e.employee_number,
                                      rp.type, rp.amount, rp.reason, rp.record_date,
                                      rp.created_at, rp.updated_at
                               FROM t_reward_penalty rp
                               LEFT JOIN t_employee e ON rp.employee_id = e.id
                               WHERE rp.employee_id = @employeeId 
                                 AND rp.is_deleted = FALSE
                                 AND rp.record_date >= @hireDate
                               ORDER BY rp.record_date DESC
                               """;

                    cmd = new MySqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@employeeId", employeeId);
                    cmd.Parameters.AddWithValue("@hireDate", employee.HireDate.Value.Date);
                }
                else
                {
                    // 如果员工没有入职日期，使用原始查询
                    sql = """
                               SELECT rp.id, rp.employee_id, e.name as employee_name, e.employee_number,
                                      rp.type, rp.amount, rp.reason, rp.record_date,
                                      rp.created_at, rp.updated_at
                               FROM t_reward_penalty rp
                               LEFT JOIN t_employee e ON rp.employee_id = e.id
                               WHERE rp.employee_id = @employeeId AND rp.is_deleted = FALSE
                               ORDER BY rp.created_at DESC
                               """;

                    cmd = new MySqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@employeeId", employeeId);
                }

                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    rewardPunishments.Add(new RewardPunishment
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        EmployeeId = reader.GetInt32(reader.GetOrdinal("employee_id")),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString(reader.GetOrdinal("employee_name")),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString(reader.GetOrdinal("employee_number")),
                        Type = reader.GetString(reader.GetOrdinal("type")),
                        Amount = reader.GetDecimal(reader.GetOrdinal("amount")),
                        Reason = reader.IsDBNull(reader.GetOrdinal("reason")) ? null : reader.GetString(reader.GetOrdinal("reason")),
                        RecordDate = reader.GetDateTime(reader.GetOrdinal("record_date")),
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据员工ID查找奖罚记录时发生错误: {ex.Message}");
            }

            return rewardPunishments;
        }

        // 搜索奖罚记录
        public async Task<List<RewardPunishment>> SearchRewardPunishmentsAsync(string searchText)
        {
            var rewardPunishments = new List<RewardPunishment>();

            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               SELECT rp.id, rp.employee_id, e.name as employee_name, e.employee_number,
                                      rp.type, rp.amount, rp.reason, rp.record_date,
                                      rp.created_at, rp.updated_at, e.hire_date
                               FROM t_reward_penalty rp
                               LEFT JOIN t_employee e ON rp.employee_id = e.id
                               WHERE (e.name LIKE @searchText OR e.employee_number LIKE @searchText)
                                    AND rp.is_deleted = FALSE
                               ORDER BY rp.created_at DESC
                               """;

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");
                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    // 检查奖惩记录日期是否在员工入职日期之后
                    var hireDate = reader.IsDBNull(reader.GetOrdinal("hire_date")) ? 
                                  (DateTime?)null : reader.GetDateTime("hire_date");
                    var recordDate = reader.GetDateTime("record_date");
                    
                    if (hireDate.HasValue && recordDate < hireDate.Value)
                    {
                        // 跳过入职日期之前的记录
                        continue;
                    }
                    
                    rewardPunishments.Add(new RewardPunishment
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        EmployeeId = reader.GetInt32(reader.GetOrdinal("employee_id")),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString(reader.GetOrdinal("employee_name")),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString(reader.GetOrdinal("employee_number")),
                        Type = reader.GetString(reader.GetOrdinal("type")),
                        Amount = reader.GetDecimal(reader.GetOrdinal("amount")),
                        Reason = reader.IsDBNull(reader.GetOrdinal("reason")) ? null : reader.GetString(reader.GetOrdinal("reason")),
                        RecordDate = recordDate,
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"搜索奖罚记录时发生错误: {ex.Message}");
            }

            return rewardPunishments;
        }

        // 获取所有员工列表（用于下拉选择）
        public async Task<List<Employee>> GetAllEmployeesAsync()
        {
            var employees = new List<Employee>();

            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               SELECT id, employee_number, name
                               FROM t_employee
                               WHERE is_deleted = FALSE
                               ORDER BY name
                               """;

                await using var cmd = new MySqlCommand(sql, connection);
                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    employees.Add(new Employee
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        EmployeeNumber = reader.GetString(reader.GetOrdinal("employee_number")),
                        Name = reader.GetString(reader.GetOrdinal("name"))
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取所有员工列表时发生错误: {ex.Message}");
            }

            return employees;
        }

        // 根据姓名或工号查找员工
        public async Task<Employee?> FindEmployeeByNumberAsync(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                Console.WriteLine("搜索文本为空");
                return null;
            }

            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               SELECT id, employee_number, name, status
                               FROM t_employee
                               WHERE (employee_number = @searchText) 
                                 AND is_deleted = FALSE
                               """;

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@searchText", searchText.Trim());

                await using var reader = await cmd.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    var employee = new Employee
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        EmployeeNumber = reader.GetString(reader.GetOrdinal("employee_number")),
                        Name = reader.GetString(reader.GetOrdinal("name")),
                        Status = reader.IsDBNull(reader.GetOrdinal("status")) ? null : reader.GetString(reader.GetOrdinal("status"))
                    };

                    Console.WriteLine($"找到员工: ID={employee.Id}, 工号={employee.EmployeeNumber}, 姓名={employee.Name}, 状态={employee.Status}");
                    return employee;
                }

                Console.WriteLine($"未找到员工: {searchText}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据姓名或工号查找员工时发生错误: {ex.Message}");
                Console.WriteLine($"搜索文本: {searchText}");
                return null;
            }
        }

        // 测试方法：获取所有员工列表用于调试
        public async Task<List<Employee>> GetAllEmployeesForDebugAsync()
        {
            var employees = new List<Employee>();

            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = """
                               SELECT id, employee_number, name, status
                               FROM t_employee
                               WHERE is_deleted = FALSE
                               ORDER BY name
                               """;

                await using var cmd = new MySqlCommand(sql, connection);
                await using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    employees.Add(new Employee
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        EmployeeNumber = reader.GetString(reader.GetOrdinal("employee_number")),
                        Name = reader.GetString(reader.GetOrdinal("name")),
                        Status = reader.IsDBNull(reader.GetOrdinal("status")) ? null : reader.GetString(reader.GetOrdinal("status"))
                    });
                }

                Console.WriteLine($"数据库中共有 {employees.Count} 个在职员工");
                foreach (var emp in employees)
                {
                    Console.WriteLine($"员工: ID={emp.Id}, 工号={emp.EmployeeNumber}, 姓名={emp.Name}, 状态={emp.Status}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取所有员工列表时发生错误: {ex.Message}");
            }

            return employees;
        }
    }
}