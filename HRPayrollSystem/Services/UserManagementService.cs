using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;

namespace HRPayrollSystem.Services;

public class UserManagementService
{
    // 最佳实践：这个字符串应该从配置文件加载

    public string HashPassword(string password)
    {
        var hashedBytes = SHA256.HashData(Encoding.UTF8.GetBytes(password));
        return Convert.ToBase64String(hashedBytes);
    }

    // 验证密码
    public bool VerifyPassword(string password, string hashedPassword)
    {
        var inputHash = HashPassword(password);
        return inputHash.Equals(hashedPassword);
    }

    // 用户登录验证
    public async Task<User?> LoginAsync(string employeeNumber, string password)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT id, employee_number, role, password_hash, created_at, updated_at 
                               FROM t_user 
                               WHERE employee_number = @employeeNumber AND is_deleted = FALSE
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@employeeNumber", employeeNumber);

            await using var reader = await cmd.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                var storedPasswordHash = reader.GetString("password_hash");
                
                // 验证密码
                if (VerifyPassword(password, storedPasswordHash))
                {
                    return new User
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeNumber = reader.GetString("employee_number"),
                        Role = reader.GetString("role"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    };
                }
            }
            
            return null; // 登录失败
        }
        catch (Exception ex)
        {
            Console.WriteLine($"登录验证时发生错误: {ex.Message}");
            return null;
        }
    }

    // 插入新用户记录
    public async Task<bool> DeleteUserAsync(int id)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               UPDATE t_user
                               SET is_deleted = TRUE, updated_at = NOW()
                               WHERE id = @id
                               """;
            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@id", id);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"删除用户记录时发生错误: {ex.Message}");
            return false;
        }
    }
    // 插入新用户记录
    public async Task<bool> InsertUserAsync(User user)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               INSERT INTO t_user (employee_number, password_hash, role) 
                               VALUES (@employeeNumber, @password_hash, @role)
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@employeeNumber", user.EmployeeNumber);
            cmd.Parameters.AddWithValue("@password_hash", user.PasswordHash);
            cmd.Parameters.AddWithValue("@role", user.Role);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"插入用户记录时发生错误: {ex.Message}");
            return false;
        }
    }

    // 更新用户记录
    public async Task<bool> UpdateUserAsync(User user)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();
            const string sql1 = """
                                UPDATE t_user 
                                SET employee_number = @employeeNumber, 
                                    role = @role, 
                                    updated_at = NOW()
                                WHERE id = @id AND is_deleted = FALSE
                                """;
            const string sql2 = """
                               UPDATE t_user 
                               SET employee_number = @employeeNumber, 
                                   password_hash = @password_hash, 
                                   role = @role, 
                                   updated_at = NOW()
                               WHERE id = @id AND is_deleted = FALSE
                               """;
            var sql = string.IsNullOrEmpty(user.PasswordHash) ? sql1 : sql2;
            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@id", user.Id);
            cmd.Parameters.AddWithValue("@employeeNumber", user.EmployeeNumber);
            if (!string.IsNullOrEmpty(user.PasswordHash))
            {
                cmd.Parameters.AddWithValue("@password_hash", user.PasswordHash);
            }
            cmd.Parameters.AddWithValue("@role", user.Role);

            var result = await cmd.ExecuteNonQueryAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新用户记录时发生错误: {ex.Message}");
            return false;
        }
    }
    public async Task<IEnumerable<string>> SearchTop10EmployeeNumbersAndNamesAsync(string searchText)
    {
        var results = new List<string>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync(); // Open() 的异步版本

            const string sql = """
                               SELECT id, employee_number, name, CONCAT(employee_number, ' ', name) as to_search 
                               FROM t_employee 
                               WHERE CONCAT(employee_number, ' ', name) LIKE @searchText
                               LIMIT 10
                               """;
        
            await using var cmd = new MySqlCommand(sql, connection);

            cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");

            // 使用 ExecuteReaderAsync() 的异步版本
            await using var reader = await cmd.ExecuteReaderAsync();

            // 异步读取每一行

            while (await reader.ReadAsync())
            {
                Console.WriteLine($"Employee: {reader.GetString("to_search")}");
                results.Add(reader.GetString("to_search"));
            }


            return results;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"搜索员工编号和姓名时发生错误: {ex.Message}");
        }

        return results;
    }
    // 实现接口中的异步方法
    public async Task<List<User>> FindAllAsync()
    {
        var users = new List<User>();

        // 使用 "await using" 来异步地释放资源
        await using var connection = new MySqlConnection(ConnectionString);
        await connection.OpenAsync(); // Open() 的异步版本

        const string sql =
            "SELECT id, employee_number, role, created_at, updated_at FROM t_user WHERE is_deleted = FALSE";
        await using var cmd = new MySqlCommand(sql, connection);

        // 使用 ExecuteReaderAsync() 的异步版本
        await using var reader = await cmd.ExecuteReaderAsync();

        // 异步读取每一行
        while (await reader.ReadAsync())
        {
            var user = new User
            {
                Id = reader.GetInt32("id"),
                EmployeeNumber = reader.GetString("employee_number"),
                Role = reader.GetString("role"),
                CreatedAt = reader.GetDateTime("created_at"),
                UpdatedAt = reader.GetDateTime("updated_at")
            };
            Console.WriteLine($"User: {reader.GetString("employee_number")}, Role: {reader.GetString("role")}");
            users.Add(user);
        }

        return users;
    }

    // 搜索用户
    public async Task<List<User>> SearchUsersAsync(string searchText)
    {
        var users = new List<User>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT id, employee_number, role, created_at, updated_at 
                               FROM t_user 
                               WHERE is_deleted = FALSE 
                               AND (employee_number LIKE @searchText OR role LIKE @searchText)
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");

            await using var reader = await cmd.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                var user = new User
                {
                    Id = reader.GetInt32("id"),
                    EmployeeNumber = reader.GetString("employee_number"),
                    Role = reader.GetString("role"),
                    CreatedAt = reader.GetDateTime("created_at"),
                    UpdatedAt = reader.GetDateTime("updated_at")
                };
                users.Add(user);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"搜索用户时发生错误: {ex.Message}");
        }

        return users;
    }

    // 分页获取用户列表
    public async Task<List<User>> FindByPageAsync(int pageNumber, int pageSize)
    {
        var users = new List<User>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT id, employee_number, role, created_at, updated_at 
                               FROM t_user 
                               WHERE is_deleted = FALSE 
                               ORDER BY created_at DESC
                               LIMIT @pageSize OFFSET @offset
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@pageSize", pageSize);
            cmd.Parameters.AddWithValue("@offset", (pageNumber - 1) * pageSize);

            await using var reader = await cmd.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                var user = new User
                {
                    Id = reader.GetInt32("id"),
                    EmployeeNumber = reader.GetString("employee_number"),
                    Role = reader.GetString("role"),
                    CreatedAt = reader.GetDateTime("created_at"),
                    UpdatedAt = reader.GetDateTime("updated_at")
                };
                users.Add(user);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"分页获取用户时发生错误: {ex.Message}");
        }

        return users;
    }

    // 获取用户总数
    public async Task<int> CountTotalUsersAsync()
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = "SELECT COUNT(*) FROM t_user WHERE is_deleted = FALSE";
            await using var cmd = new MySqlCommand(sql, connection);

            var result = await cmd.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取用户总数时发生错误: {ex.Message}");
            return 0;
        }
    }

    // 分页搜索用户
    public async Task<List<User>> SearchUsersByPageAsync(string searchText, int pageNumber, int pageSize)
    {
        var users = new List<User>();

        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT id, employee_number, role, created_at, updated_at 
                               FROM t_user 
                               WHERE is_deleted = FALSE 
                               AND (employee_number LIKE @searchText OR role LIKE @searchText)
                               ORDER BY created_at DESC
                               LIMIT @pageSize OFFSET @offset
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");
            cmd.Parameters.AddWithValue("@pageSize", pageSize);
            cmd.Parameters.AddWithValue("@offset", (pageNumber - 1) * pageSize);

            await using var reader = await cmd.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                var user = new User
                {
                    Id = reader.GetInt32("id"),
                    EmployeeNumber = reader.GetString("employee_number"),
                    Role = reader.GetString("role"),
                    CreatedAt = reader.GetDateTime("created_at"),
                    UpdatedAt = reader.GetDateTime("updated_at")
                };
                users.Add(user);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"分页搜索用户时发生错误: {ex.Message}");
        }

        return users;
    }

    // 获取搜索结果总数
    public async Task<int> CountSearchResultsAsync(string searchText)
    {
        try
        {
            await using var connection = new MySqlConnection(ConnectionString);
            await connection.OpenAsync();

            const string sql = """
                               SELECT COUNT(*) 
                               FROM t_user 
                               WHERE is_deleted = FALSE 
                               AND (employee_number LIKE @searchText OR role LIKE @searchText)
                               """;

            await using var cmd = new MySqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");

            var result = await cmd.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取搜索结果总数时发生错误: {ex.Message}");
            return 0;
        }
    }
}