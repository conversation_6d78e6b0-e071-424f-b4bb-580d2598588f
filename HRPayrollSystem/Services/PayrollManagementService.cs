using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;
using System.IO;

namespace HRPayrollSystem.Services
{
    public class PayrollManagementService
    {
        private readonly EmployeeManagementService _employeeService;
        private readonly RewardPunishmentManagementService _rewardPunishmentService;
        private readonly AttendanceManagementService _attendanceService;

        public PayrollManagementService(
            EmployeeManagementService employeeService,
            RewardPunishmentManagementService rewardPunishmentService,
            AttendanceManagementService attendanceService)
        {
            _employeeService = employeeService;
            _rewardPunishmentService = rewardPunishmentService;
            _attendanceService = attendanceService;
        }

        // 生成月度工资单
        public async Task<bool> GenerateMonthlyPayrollsAsync(string payMonth)
        {
            try
            {
                // 获取所有员工
                var employees = await _employeeService.FindAllAsync();
                if (employees == null || !employees.Any())
                {
                    return false;
                }

                // 获取当月日历信息
                int year = int.Parse(payMonth.Split('-')[0]);
                int month = int.Parse(payMonth.Split('-')[1]);
                var daysInMonth = DateTime.DaysInMonth(year, month);
                var firstDayOfMonth = new DateTime(year, month, 1);
                var lastDayOfMonth = new DateTime(year, month, daysInMonth);
                
                // 计算当月应出勤工作日天数（每周5个工作日，排除周末）
                int workDaysInMonth = 0;
                for (var day = firstDayOfMonth; day <= lastDayOfMonth; day = day.AddDays(1))
                {
                    if (day.DayOfWeek != DayOfWeek.Saturday && day.DayOfWeek != DayOfWeek.Sunday)
                    {
                        workDaysInMonth++;
                    }
                }

                // 批量生成工资单
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();
                
                // 开始事务
                await using var transaction = await connection.BeginTransactionAsync();
                
                try
                {
                    foreach (var employee in employees)
                    {
                        // 只处理在职员工
                        if (employee.Status != "在职" && employee.Status != "试用期")
                            continue;
                            
                        // 如果员工入职时间晚于当月最后一天，跳过该员工（不生成工资单）
                        if (employee.HireDate.HasValue && employee.HireDate.Value > lastDayOfMonth)
                            continue;

                        // 检查是否已存在该月工资单
                        const string checkExistSql = @"
                            SELECT COUNT(*) FROM t_payroll 
                            WHERE employee_id = @employeeId AND pay_month = @payMonth AND is_deleted = FALSE";
                        await using var checkCmd = new MySqlCommand(checkExistSql, connection, transaction);
                        checkCmd.Parameters.AddWithValue("@employeeId", employee.Id);
                        checkCmd.Parameters.AddWithValue("@payMonth", payMonth);
                        
                        var existingCount = Convert.ToInt32(await checkCmd.ExecuteScalarAsync());
                        if (existingCount > 0)
                            continue; // 跳过已有工资单的员工
                        
                        // 获取员工本月考勤信息
                        var attendances = await _attendanceService.FindByEmployeeAndMonthAsync(employee.Id, year, month);
                        
                        // 计算考勤扣款（每次缺勤扣100元）
                        decimal attendanceDeduction = CalculateAttendanceDeduction(attendances);
                        
                        // 获取奖惩信息
                        decimal rewardAmount = await GetMonthlyRewardAmount(employee.Id, year, month);
                        decimal penaltyAmount = await GetMonthlyPenaltyAmount(employee.Id, year, month);
                        
                        // 计算基本工资（根据是否在试用期调整）
                        decimal baseSalary = employee.BaseSalary;
                        
                        // 判断员工是否在试用期内
                        bool isInProbation = false;
                        if (employee.HireDate.HasValue && employee.ProbationEndDate.HasValue)
                        {
                            // 判断当月是否在试用期内（使用月末作为参考日期）
                            isInProbation = lastDayOfMonth >= employee.HireDate.Value && 
                                           lastDayOfMonth <= employee.ProbationEndDate.Value;
                        }
                        
                        // 如果在试用期内，基本工资为正式工资的60%
                        if (isInProbation)
                        {
                            baseSalary = baseSalary * 0.6m;
                        }
                        
                        // 计算实发工资 = 基本工资 + 奖励金额 - 处罚金额 - 考勤扣款
                        decimal netSalary = baseSalary + rewardAmount - penaltyAmount - attendanceDeduction;
                        
                        // 插入工资单记录
                        const string insertSql = @"
                            INSERT INTO t_payroll (
                                employee_id, pay_month, base_salary, reward_amount,
                                penalty_amount, attendance_deduction, tax, net_salary, status
                            ) VALUES (
                                @employeeId, @payMonth, @baseSalary, @rewardAmount,
                                @penaltyAmount, @attendanceDeduction, @tax, @netSalary, @status
                            )";
                        
                        await using var insertCmd = new MySqlCommand(insertSql, connection, transaction);
                        insertCmd.Parameters.AddWithValue("@employeeId", employee.Id);
                        insertCmd.Parameters.AddWithValue("@payMonth", payMonth);
                        insertCmd.Parameters.AddWithValue("@baseSalary", baseSalary);
                        insertCmd.Parameters.AddWithValue("@rewardAmount", rewardAmount);
                        insertCmd.Parameters.AddWithValue("@penaltyAmount", penaltyAmount);
                        insertCmd.Parameters.AddWithValue("@attendanceDeduction", attendanceDeduction);
                        insertCmd.Parameters.AddWithValue("@tax", 0); // 不再计算个税
                        insertCmd.Parameters.AddWithValue("@netSalary", netSalary);
                        insertCmd.Parameters.AddWithValue("@status", "待确认");
                        
                        await insertCmd.ExecuteNonQueryAsync();
                    }
                    
                    // 提交事务
                    await transaction.CommitAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();
                    Console.WriteLine($"生成工资单时发生错误: {ex.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成工资单时发生错误: {ex.Message}");
                return false;
            }
        }
        
        // 获取月度工资单列表
        public async Task<List<Payroll>> GetPayrollsByMonthAsync(string payMonth, int page = 1, int pageSize = 20)
        {
            var payrolls = new List<Payroll>();
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    SELECT p.*, e.name as employee_name, e.employee_number,
                           d.name as department_name, pos.name as position_name
                    FROM t_payroll p
                    LEFT JOIN t_employee e ON p.employee_id = e.id
                    LEFT JOIN t_department d ON e.department_id = d.id
                    LEFT JOIN t_position pos ON e.position_id = pos.id
                    WHERE p.pay_month = @payMonth AND p.is_deleted = FALSE
                    ORDER BY p.id
                    LIMIT @offset, @limit";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@payMonth", payMonth);
                cmd.Parameters.AddWithValue("@offset", (page - 1) * pageSize);
                cmd.Parameters.AddWithValue("@limit", pageSize);

                await using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    payrolls.Add(new Payroll
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? null : reader.GetString("department_name"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? null : reader.GetString("position_name"),
                        PayMonth = reader.GetString("pay_month"),
                        BaseSalary = reader.GetDecimal("base_salary"),
                        RewardAmount = reader.GetDecimal("reward_amount"),
                        PenaltyAmount = reader.GetDecimal("penalty_amount"),
                        AttendanceDeduction = reader.GetDecimal("attendance_deduction"),
                        Tax = reader.GetDecimal("tax"),
                        NetSalary = reader.GetDecimal("net_salary"),
                        Status = reader.GetString("status"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    });
                }

                return payrolls;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工资单列表时发生错误: {ex.Message}");
                return payrolls;
            }
        }
        
        // 获取指定ID的工资单
        public async Task<Payroll?> GetPayrollByIdAsync(int id)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    SELECT p.*, e.name as employee_name, e.employee_number,
                           d.name as department_name, pos.name as position_name
                    FROM t_payroll p
                    LEFT JOIN t_employee e ON p.employee_id = e.id
                    LEFT JOIN t_department d ON e.department_id = d.id
                    LEFT JOIN t_position pos ON e.position_id = pos.id
                    WHERE p.id = @id AND p.is_deleted = FALSE";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@id", id);

                await using var reader = await cmd.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new Payroll
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? null : reader.GetString("department_name"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? null : reader.GetString("position_name"),
                        PayMonth = reader.GetString("pay_month"),
                        BaseSalary = reader.GetDecimal("base_salary"),
                        RewardAmount = reader.GetDecimal("reward_amount"),
                        PenaltyAmount = reader.GetDecimal("penalty_amount"),
                        AttendanceDeduction = reader.GetDecimal("attendance_deduction"),
                        Tax = reader.GetDecimal("tax"),
                        NetSalary = reader.GetDecimal("net_salary"),
                        Status = reader.GetString("status"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工资单详情时发生错误: {ex.Message}");
                return null;
            }
        }
        
        // 更新工资单状态
        public async Task<bool> UpdatePayrollStatusAsync(int payrollId, string status)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    UPDATE t_payroll
                    SET status = @status,
                        updated_at = NOW()
                    WHERE id = @id";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@id", payrollId);
                cmd.Parameters.AddWithValue("@status", status);

                var result = await cmd.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新工资单状态时发生错误: {ex.Message}");
                return false;
            }
        }
        
        // 批量更新工资单状态
        public async Task<bool> UpdatePayrollStatusBatchAsync(List<int> ids, string status)
        {
            try
            {
                if (ids == null || !ids.Any())
                    return false;

                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                // 构建IN子句
                string idList = string.Join(",", ids);
                string sql = $@"
                    UPDATE t_payroll
                    SET status = @status,
                        updated_at = NOW()
                    WHERE id IN ({idList}) AND is_deleted = FALSE";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@status", status);

                var result = await cmd.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量更新工资单状态时发生错误: {ex.Message}");
                return false;
            }
        }

        // 删除工资单
        public async Task<bool> DeletePayrollAsync(int id)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    UPDATE t_payroll
                    SET is_deleted = TRUE,
                        updated_at = NOW()
                    WHERE id = @id";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@id", id);

                var result = await cmd.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除工资单时发生错误: {ex.Message}");
                return false;
            }
        }
        
        // 统计指定月份的工资单数量
        public async Task<int> CountPayrollsByMonthAsync(string payMonth)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    SELECT COUNT(*)
                    FROM t_payroll
                    WHERE pay_month = @payMonth AND is_deleted = FALSE";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@payMonth", payMonth);

                var result = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"统计工资单数量时发生错误: {ex.Message}");
                return 0;
            }
        }
        
        // 根据搜索条件获取工资单列表
        public async Task<List<Payroll>> SearchPayrollsAsync(string searchText, string payMonth, int page = 1, int pageSize = 20)
        {
            var payrolls = new List<Payroll>();
            try
            {
                // 解析年月
                int year = int.Parse(payMonth.Split('-')[0]);
                int month = int.Parse(payMonth.Split('-')[1]);
                var daysInMonth = DateTime.DaysInMonth(year, month);
                var lastDayOfMonth = new DateTime(year, month, daysInMonth);
                
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                string sql;
                MySqlCommand cmd;

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // 如果没有搜索条件，返回所有记录
                    sql = @"
                        SELECT p.*, e.name as employee_name, e.employee_number,
                               d.name as department_name, pos.name as position_name
                        FROM t_payroll p
                        LEFT JOIN t_employee e ON p.employee_id = e.id
                        LEFT JOIN t_department d ON e.department_id = d.id
                        LEFT JOIN t_position pos ON e.position_id = pos.id
                        WHERE p.pay_month = @payMonth AND p.is_deleted = FALSE
                        AND (e.hire_date IS NULL OR e.hire_date <= @lastDayOfMonth)
                        ORDER BY p.id
                        LIMIT @offset, @limit";

                    cmd = new MySqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@payMonth", payMonth);
                    cmd.Parameters.AddWithValue("@lastDayOfMonth", lastDayOfMonth);
                    cmd.Parameters.AddWithValue("@offset", (page - 1) * pageSize);
                    cmd.Parameters.AddWithValue("@limit", pageSize);
                }
                else
                {
                    // 有搜索条件，按员工姓名或编号模糊查询
                    sql = @"
                        SELECT p.*, e.name as employee_name, e.employee_number,
                               d.name as department_name, pos.name as position_name
                        FROM t_payroll p
                        LEFT JOIN t_employee e ON p.employee_id = e.id
                        LEFT JOIN t_department d ON e.department_id = d.id
                        LEFT JOIN t_position pos ON e.position_id = pos.id
                        WHERE p.pay_month = @payMonth 
                          AND p.is_deleted = FALSE
                          AND (e.name LIKE @searchText OR e.employee_number LIKE @searchText)
                          AND (e.hire_date IS NULL OR e.hire_date <= @lastDayOfMonth)
                        ORDER BY p.id
                        LIMIT @offset, @limit";

                    cmd = new MySqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@payMonth", payMonth);
                    cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");
                    cmd.Parameters.AddWithValue("@lastDayOfMonth", lastDayOfMonth);
                    cmd.Parameters.AddWithValue("@offset", (page - 1) * pageSize);
                    cmd.Parameters.AddWithValue("@limit", pageSize);
                }

                await using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    payrolls.Add(new Payroll
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? null : reader.GetString("department_name"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? null : reader.GetString("position_name"),
                        PayMonth = reader.GetString("pay_month"),
                        BaseSalary = reader.GetDecimal("base_salary"),
                        RewardAmount = reader.GetDecimal("reward_amount"),
                        PenaltyAmount = reader.GetDecimal("penalty_amount"),
                        AttendanceDeduction = reader.GetDecimal("attendance_deduction"),
                        Tax = reader.GetDecimal("tax"),
                        NetSalary = reader.GetDecimal("net_salary"),
                        Status = reader.GetString("status"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    });
                }

                return payrolls;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"搜索工资单时发生错误: {ex.Message}");
                return payrolls;
            }
        }

        // 根据搜索条件统计工资单数量
        public async Task<int> CountSearchPayrollsAsync(string searchText, string payMonth)
        {
            try
            {
                // 解析年月
                int year = int.Parse(payMonth.Split('-')[0]);
                int month = int.Parse(payMonth.Split('-')[1]);
                var daysInMonth = DateTime.DaysInMonth(year, month);
                var lastDayOfMonth = new DateTime(year, month, daysInMonth);
                
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                string sql;
                MySqlCommand cmd;

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // 如果没有搜索条件，返回所有记录数量
                    sql = @"
                        SELECT COUNT(*)
                        FROM t_payroll p
                        LEFT JOIN t_employee e ON p.employee_id = e.id
                        WHERE p.pay_month = @payMonth 
                        AND p.is_deleted = FALSE
                        AND (e.hire_date IS NULL OR e.hire_date <= @lastDayOfMonth)";

                    cmd = new MySqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@payMonth", payMonth);
                    cmd.Parameters.AddWithValue("@lastDayOfMonth", lastDayOfMonth);
                }
                else
                {
                    // 有搜索条件，按员工姓名或编号模糊查询
                    sql = @"
                        SELECT COUNT(*)
                        FROM t_payroll p
                        LEFT JOIN t_employee e ON p.employee_id = e.id
                        WHERE p.pay_month = @payMonth 
                          AND p.is_deleted = FALSE
                          AND (e.name LIKE @searchText OR e.employee_number LIKE @searchText)
                          AND (e.hire_date IS NULL OR e.hire_date <= @lastDayOfMonth)";

                    cmd = new MySqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@payMonth", payMonth);
                    cmd.Parameters.AddWithValue("@searchText", $"%{searchText}%");
                    cmd.Parameters.AddWithValue("@lastDayOfMonth", lastDayOfMonth);
                }

                var result = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"统计搜索工资单数量时发生错误: {ex.Message}");
                return 0;
            }
        }
        
        // 导出工资单为CSV格式
        public async Task<string> ExportPayrollsToCSVAsync(List<int> payrollIds)
        {
            try
            {
                if (payrollIds == null || !payrollIds.Any())
                    return string.Empty;

                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                // 构建IN子句
                string idList = string.Join(",", payrollIds);
                string sql = $@"
                    SELECT p.*, e.name as employee_name, e.employee_number,
                           d.name as department_name, pos.name as position_name
                    FROM t_payroll p
                    LEFT JOIN t_employee e ON p.employee_id = e.id
                    LEFT JOIN t_department d ON e.department_id = d.id
                    LEFT JOIN t_position pos ON e.position_id = pos.id
                    WHERE p.id IN ({idList}) AND p.is_deleted = FALSE
                    ORDER BY p.pay_month DESC, e.name";

                await using var cmd = new MySqlCommand(sql, connection);
                await using var reader = await cmd.ExecuteReaderAsync();

                // 创建CSV内容
                using var sw = new StringWriter();
                
                // 写入CSV头部
                sw.WriteLine("工号,姓名,部门,职位,工资月份,基本工资,奖励金额,处罚金额,考勤扣款,实发工资,状态");
                
                // 写入数据行
                while (await reader.ReadAsync())
                {
                    string employeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? "" : reader.GetString("employee_number");
                    string employeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? "" : reader.GetString("employee_name");
                    string department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? "" : reader.GetString("department_name");
                    string position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? "" : reader.GetString("position_name");
                    string payMonth = reader.GetString("pay_month");
                    decimal baseSalary = reader.GetDecimal("base_salary");
                    decimal rewardAmount = reader.GetDecimal("reward_amount");
                    decimal penaltyAmount = reader.GetDecimal("penalty_amount");
                    decimal attendanceDeduction = reader.GetDecimal("attendance_deduction");
                    decimal netSalary = reader.GetDecimal("net_salary");
                    string status = reader.GetString("status");
                    
                    sw.WriteLine($"{employeeNumber},{employeeName},{department},{position},{payMonth},{baseSalary},{rewardAmount},{penaltyAmount},{attendanceDeduction},{netSalary},{status}");
                }
                
                return sw.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出工资单时发生错误: {ex.Message}");
                return string.Empty;
            }
        }

        // 导出工资单为Excel格式
        public async Task<string> ExportPayrollsToExcelAsync(string payMonth)
        {
            try
            {
                // 获取该月的所有工资单
                var payrolls = await GetPayrollsByMonthAsync(payMonth);
                if (payrolls == null || !payrolls.Any())
                {
                    return string.Empty;
                }
                
                // 创建Excel工作簿
                using var workbook = new ClosedXML.Excel.XLWorkbook();
                var worksheet = workbook.Worksheets.Add($"{payMonth} 工资表");
                
                // 设置标题行
                worksheet.Cell(1, 1).Value = "工号";
                worksheet.Cell(1, 2).Value = "姓名";
                worksheet.Cell(1, 3).Value = "部门";
                worksheet.Cell(1, 4).Value = "职位";
                worksheet.Cell(1, 5).Value = "基本工资";
                worksheet.Cell(1, 6).Value = "奖励金额";
                worksheet.Cell(1, 7).Value = "处罚金额";
                worksheet.Cell(1, 8).Value = "考勤扣款";
                worksheet.Cell(1, 9).Value = "实发工资";
                worksheet.Cell(1, 10).Value = "状态";
                
                // 设置标题行样式
                var headerRow = worksheet.Row(1);
                headerRow.Style.Font.Bold = true;
                headerRow.Style.Fill.BackgroundColor = ClosedXML.Excel.XLColor.LightGray;
                headerRow.Style.Alignment.Horizontal = ClosedXML.Excel.XLAlignmentHorizontalValues.Center;
                
                // 填充数据
                for (int i = 0; i < payrolls.Count; i++)
                {
                    var row = i + 2; // 从第二行开始
                    worksheet.Cell(row, 1).Value = payrolls[i].EmployeeNumber ?? "";
                    worksheet.Cell(row, 2).Value = payrolls[i].EmployeeName ?? "";
                    worksheet.Cell(row, 3).Value = payrolls[i].Department ?? "";
                    worksheet.Cell(row, 4).Value = payrolls[i].Position ?? "";
                    worksheet.Cell(row, 5).Value = payrolls[i].BaseSalary;
                    worksheet.Cell(row, 6).Value = payrolls[i].RewardAmount;
                    worksheet.Cell(row, 7).Value = payrolls[i].PenaltyAmount;
                    worksheet.Cell(row, 8).Value = payrolls[i].AttendanceDeduction;
                    worksheet.Cell(row, 9).Value = payrolls[i].NetSalary;
                    worksheet.Cell(row, 10).Value = payrolls[i].Status;
                    
                    // 设置金额单元格格式
                    for (int col = 5; col <= 9; col++)
                    {
                        worksheet.Cell(row, col).Style.NumberFormat.Format = "¥#,##0.00";
                    }
                }
                
                // 自动调整列宽
                worksheet.Columns().AdjustToContents();
                
                // 添加合计行
                var totalRow = payrolls.Count + 2;
                worksheet.Cell(totalRow, 1).Value = "合计";
                worksheet.Cell(totalRow, 5).FormulaA1 = $"SUM(E2:E{totalRow-1})";
                worksheet.Cell(totalRow, 6).FormulaA1 = $"SUM(F2:F{totalRow-1})";
                worksheet.Cell(totalRow, 7).FormulaA1 = $"SUM(G2:G{totalRow-1})";
                worksheet.Cell(totalRow, 8).FormulaA1 = $"SUM(H2:H{totalRow-1})";
                worksheet.Cell(totalRow, 9).FormulaA1 = $"SUM(I2:I{totalRow-1})";
                
                // 设置合计行样式
                var totalRowRange = worksheet.Range(totalRow, 1, totalRow, 10);
                totalRowRange.Style.Font.Bold = true;
                totalRowRange.Style.Fill.BackgroundColor = ClosedXML.Excel.XLColor.LightGray;
                
                // 设置金额单元格格式
                for (int col = 5; col <= 9; col++)
                {
                    worksheet.Cell(totalRow, col).Style.NumberFormat.Format = "¥#,##0.00";
                }
                
                // 创建保存目录
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                var exportDir = Path.Combine(documentsPath, "HRPayrollSystem", "Exports");
                Directory.CreateDirectory(exportDir);
                
                // 保存文件
                var fileName = $"工资表_{payMonth}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                var filePath = Path.Combine(exportDir, fileName);
                
                workbook.SaveAs(filePath);
                
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出工资单到Excel时发生错误: {ex.Message}");
                return string.Empty;
            }
        }

        // 计算考勤扣款（每次缺勤扣100元）
        private decimal CalculateAttendanceDeduction(IEnumerable<Attendance> attendances)
        {
            decimal deduction = 0;
            int leaveCount = 0; // 请假次数计数
            
            foreach (var attendance in attendances)
            {
                switch(attendance.Status)
                {
                    case "迟到":
                    case "早退":
                        // 迟到或早退一次扣50元
                        deduction += 50m;
                        break;
                    case "旷工":
                        // 旷工一次扣100元
                        deduction += 100m;
                        break;
                    case "请假":
                        // 统计请假次数
                        leaveCount++;
                        break;
                }
            }
            
            // 请假两次以内不扣钱，两次以上的部分一次扣100
            if (leaveCount > 2)
            {
                deduction += (leaveCount - 2) * 100m;
            }
            
            return deduction;
        }
        
        // 获取当月奖励总额
        private async Task<decimal> GetMonthlyRewardAmount(int employeeId, int year, int month)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    SELECT SUM(amount) 
                    FROM t_reward_penalty
                    WHERE employee_id = @employeeId 
                    AND type = '奖励'
                    AND YEAR(record_date) = @year
                    AND MONTH(record_date) = @month
                    AND is_deleted = FALSE";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@employeeId", employeeId);
                cmd.Parameters.AddWithValue("@year", year);
                cmd.Parameters.AddWithValue("@month", month);

                var result = await cmd.ExecuteScalarAsync();
                return result == DBNull.Value ? 0m : Convert.ToDecimal(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取奖励金额时发生错误: {ex.Message}");
                return 0;
            }
        }
        
        // 获取当月处罚总额
        private async Task<decimal> GetMonthlyPenaltyAmount(int employeeId, int year, int month)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                const string sql = @"
                    SELECT SUM(amount) 
                    FROM t_reward_penalty
                    WHERE employee_id = @employeeId 
                    AND type = '处罚'
                    AND YEAR(record_date) = @year
                    AND MONTH(record_date) = @month
                    AND is_deleted = FALSE";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@employeeId", employeeId);
                cmd.Parameters.AddWithValue("@year", year);
                cmd.Parameters.AddWithValue("@month", month);

                var result = await cmd.ExecuteScalarAsync();
                return result == DBNull.Value ? 0m : Convert.ToDecimal(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取处罚金额时发生错误: {ex.Message}");
                return 0;
            }
        }
        
        // 获取指定月份的法定节假日天数
        private int GetHolidaysCount(int year, int month)
        {
            // 这里简化处理，实际应从数据库或配置中获取法定节假日信息
            // 以下为2023年中国法定节假日的大致数量（简化处理）
            if (year == 2023)
            {
                return month switch
                {
                    1 => 3, // 元旦、春节
                    2 => 0,
                    3 => 0,
                    4 => 3, // 清明、五一
                    5 => 1, // 五一剩余
                    6 => 3, // 端午
                    7 => 0,
                    8 => 0,
                    9 => 3, // 中秋、国庆
                    10 => 4, // 国庆
                    _ => 0
                };
            }
            
            // 默认情况下，每月按1天法定节假日计算
            return 1;
        }

        // 检查奖惩变动并重新计算工资单
        public async Task<bool> RefreshPayrollsAsync(string payMonth)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();
                
                // 解析年月
                int year = int.Parse(payMonth.Split('-')[0]);
                int month = int.Parse(payMonth.Split('-')[1]);
                var daysInMonth = DateTime.DaysInMonth(year, month);
                var lastDayOfMonth = new DateTime(year, month, daysInMonth);
                
                // 1. 获取指定月份的所有工资单（排除已发放的工资单）
                const string getPayrollsSql = @"
                    SELECT p.id, p.employee_id, p.pay_month, p.base_salary, 
                           p.reward_amount, p.penalty_amount, p.attendance_deduction, p.net_salary, p.status
                    FROM t_payroll p
                    WHERE p.pay_month = @payMonth AND p.is_deleted = FALSE AND p.status != '已发放'";

                await using var cmd = new MySqlCommand(getPayrollsSql, connection);
                cmd.Parameters.AddWithValue("@payMonth", payMonth);
                
                List<(int id, int employeeId, decimal originalBaseSalary, string status)> payrolls = new();
                
                await using (var reader = await cmd.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        payrolls.Add((
                            reader.GetInt32("id"),
                            reader.GetInt32("employee_id"),
                            reader.GetDecimal("base_salary"),
                            reader.GetString("status")
                        ));
                    }
                }
                
                if (!payrolls.Any())
                    return true; // 没有数据或所有工资单已发放，视为成功
                    
                // 2. 开始事务
                await using var transaction = await connection.BeginTransactionAsync();
                
                try
                {
                    foreach (var (id, employeeId, originalBaseSalary, status) in payrolls)
                    {
                        // 获取员工信息
                        var employee = await _employeeService.FindByIdAsync(employeeId);
                        if (employee == null)
                            continue;
                            
                        // 如果员工入职时间晚于当月最后一天，删除该工资单记录
                        if (employee.HireDate.HasValue && employee.HireDate.Value > lastDayOfMonth)
                        {
                            const string deleteSql = @"
                                UPDATE t_payroll
                                SET is_deleted = TRUE,
                                    updated_at = NOW()
                                WHERE id = @id";
                                
                            await using var deleteCmd = new MySqlCommand(deleteSql, connection, transaction);
                            deleteCmd.Parameters.AddWithValue("@id", id);
                            await deleteCmd.ExecuteNonQueryAsync();
                            continue;
                        }
                            
                        // 计算基本工资（根据是否在试用期调整）
                        decimal baseSalary = employee.BaseSalary;
                        
                        // 判断员工是否在试用期内
                        bool isInProbation = false;
                        if (employee.HireDate.HasValue && employee.ProbationEndDate.HasValue)
                        {
                            // 判断当月是否在试用期内
                            isInProbation = lastDayOfMonth >= employee.HireDate.Value && 
                                           lastDayOfMonth <= employee.ProbationEndDate.Value;
                        }
                        
                        // 如果在试用期内，基本工资为正式工资的60%
                        if (isInProbation)
                        {
                            baseSalary = baseSalary * 0.6m;
                        }
                        
                        // 获取最新的奖惩金额
                        decimal rewardAmount = await GetMonthlyRewardAmount(employeeId, year, month);
                        decimal penaltyAmount = await GetMonthlyPenaltyAmount(employeeId, year, month);
                        
                        // 获取最新的考勤记录并重新计算考勤扣款
                        var attendances = await _attendanceService.FindByEmployeeAndMonthAsync(employeeId, year, month);
                        decimal attendanceDeduction = CalculateAttendanceDeduction(attendances);
                        
                        // 重新计算实发工资
                        decimal netSalary = baseSalary + rewardAmount - penaltyAmount - attendanceDeduction;
                        
                        // 更新工资单
                        const string updateSql = @"
                            UPDATE t_payroll
                            SET base_salary = @baseSalary,
                                reward_amount = @rewardAmount,
                                penalty_amount = @penaltyAmount,
                                attendance_deduction = @attendanceDeduction,
                                net_salary = @netSalary,
                                updated_at = NOW()
                            WHERE id = @id";
                        
                        await using var updateCmd = new MySqlCommand(updateSql, connection, transaction);
                        updateCmd.Parameters.AddWithValue("@id", id);
                        updateCmd.Parameters.AddWithValue("@baseSalary", baseSalary);
                        updateCmd.Parameters.AddWithValue("@rewardAmount", rewardAmount);
                        updateCmd.Parameters.AddWithValue("@penaltyAmount", penaltyAmount);
                        updateCmd.Parameters.AddWithValue("@attendanceDeduction", attendanceDeduction);
                        updateCmd.Parameters.AddWithValue("@netSalary", netSalary);
                        
                        await updateCmd.ExecuteNonQueryAsync();
                    }
                    
                    // 提交事务
                    await transaction.CommitAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();
                    Console.WriteLine($"刷新工资单时发生错误: {ex.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"刷新工资单时发生错误: {ex.Message}");
                return false;
            }
        }

        // 根据员工ID获取工资单列表（支持月份区间筛选）
        public async Task<List<Payroll>> GetPayrollsByEmployeeIdAsync(int employeeId, string? startMonth = null, string? endMonth = null, int page = 1, int pageSize = 20)
        {
            var payrolls = new List<Payroll>();
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                string sql = @"
                    SELECT p.*, e.name as employee_name, e.employee_number,
                           d.name as department_name, pos.name as position_name
                    FROM t_payroll p
                    LEFT JOIN t_employee e ON p.employee_id = e.id
                    LEFT JOIN t_department d ON e.department_id = d.id
                    LEFT JOIN t_position pos ON e.position_id = pos.id
                    WHERE p.employee_id = @employeeId AND p.is_deleted = FALSE";

                // 添加月份区间筛选
                if (!string.IsNullOrEmpty(startMonth))
                {
                    sql += " AND p.pay_month >= @startMonth";
                }
                if (!string.IsNullOrEmpty(endMonth))
                {
                    sql += " AND p.pay_month <= @endMonth";
                }

                sql += " ORDER BY p.pay_month DESC, p.id DESC LIMIT @offset, @limit";

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@employeeId", employeeId);
                cmd.Parameters.AddWithValue("@offset", (page - 1) * pageSize);
                cmd.Parameters.AddWithValue("@limit", pageSize);

                if (!string.IsNullOrEmpty(startMonth))
                {
                    cmd.Parameters.AddWithValue("@startMonth", startMonth);
                }
                if (!string.IsNullOrEmpty(endMonth))
                {
                    cmd.Parameters.AddWithValue("@endMonth", endMonth);
                }

                await using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    payrolls.Add(new Payroll
                    {
                        Id = reader.GetInt32("id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        EmployeeName = reader.IsDBNull(reader.GetOrdinal("employee_name")) ? null : reader.GetString("employee_name"),
                        EmployeeNumber = reader.IsDBNull(reader.GetOrdinal("employee_number")) ? null : reader.GetString("employee_number"),
                        Department = reader.IsDBNull(reader.GetOrdinal("department_name")) ? null : reader.GetString("department_name"),
                        Position = reader.IsDBNull(reader.GetOrdinal("position_name")) ? null : reader.GetString("position_name"),
                        PayMonth = reader.GetString("pay_month"),
                        BaseSalary = reader.GetDecimal("base_salary"),
                        RewardAmount = reader.GetDecimal("reward_amount"),
                        PenaltyAmount = reader.GetDecimal("penalty_amount"),
                        AttendanceDeduction = reader.GetDecimal("attendance_deduction"),
                        Tax = reader.GetDecimal("tax"),
                        NetSalary = reader.GetDecimal("net_salary"),
                        Status = reader.GetString("status"),
                        CreatedAt = reader.GetDateTime("created_at"),
                        UpdatedAt = reader.GetDateTime("updated_at")
                    });
                }

                return payrolls;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据员工ID获取工资单列表时发生错误: {ex.Message}");
                return payrolls;
            }
        }

        // 根据员工ID统计工资单数量（支持月份区间筛选）
        public async Task<int> CountPayrollsByEmployeeIdAsync(int employeeId, string? startMonth = null, string? endMonth = null)
        {
            try
            {
                await using var connection = new MySqlConnection(ConnectionString);
                await connection.OpenAsync();

                string sql = @"
                    SELECT COUNT(*)
                    FROM t_payroll p
                    WHERE p.employee_id = @employeeId AND p.is_deleted = FALSE";

                // 添加月份区间筛选
                if (!string.IsNullOrEmpty(startMonth))
                {
                    sql += " AND p.pay_month >= @startMonth";
                }
                if (!string.IsNullOrEmpty(endMonth))
                {
                    sql += " AND p.pay_month <= @endMonth";
                }

                await using var cmd = new MySqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@employeeId", employeeId);

                if (!string.IsNullOrEmpty(startMonth))
                {
                    cmd.Parameters.AddWithValue("@startMonth", startMonth);
                }
                if (!string.IsNullOrEmpty(endMonth))
                {
                    cmd.Parameters.AddWithValue("@endMonth", endMonth);
                }

                var result = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据员工ID统计工资单数量时发生错误: {ex.Message}");
                return 0;
            }
        }
    }
}