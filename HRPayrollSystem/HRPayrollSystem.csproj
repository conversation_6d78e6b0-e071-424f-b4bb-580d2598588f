﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    </PropertyGroup>

    <ItemGroup>
        <AvaloniaResource Include="Assets\**"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="11.2.5" />
        <PackageReference Include="Avalonia.Controls.DataGrid" Version="11.2.5" />
        <PackageReference Include="Avalonia.Desktop" Version="11.2.1"/>
        <PackageReference Include="Avalonia.Themes.Fluent" Version="11.2.1"/>
        <PackageReference Include="Avalonia.Fonts.Inter" Version="11.2.1"/>
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Include="Avalonia.Diagnostics" Version="11.2.1">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Avalonia.ReactiveUI" Version="11.2.1"/>
        <PackageReference Include="ClosedXML" Version="0.102.2" />
        <PackageReference Include="FluentAvaloniaUI" Version="2.4.0-preview1" />
        <PackageReference Include="FluentIcons.Avalonia" Version="1.1.306-ci" />
        <PackageReference Include="LiveChartsCore.SkiaSharpView.Avalonia" Version="2.0.0-rc5.4" />
        <PackageReference Include="MySqlConnector" Version="2.4.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Views\Dialogs\AddEditUserDialog.axaml" />
      <UpToDateCheckInput Remove="Views\Dialogs\AssignRoleDialog.axaml" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Views\Dialogs\EditEmployeeDialogContent.axaml.cs">
        <DependentUpon>EditEmployeeDialogContent.axaml</DependentUpon>
      </Compile>
      <Compile Update="Views\Pages\PayrollManagementPage.axaml.cs">
        <DependentUpon>PayrollManagementPage.axaml</DependentUpon>
      </Compile>
    </ItemGroup>
</Project>
