using System;

namespace HRPayrollSystem.Models
{
    public record TransferLog
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public string? EmployeeName { get; set; } // 非数据库字段，用于显示
        public string? EmployeeNumber { get; set; } // 非数据库字段，用于显示
        public DateTime TransferDate { get; set; }
        public string Type { get; set; } = ""; // 调动类型：部门调动, 岗位调动
        public int? OldDepartmentId { get; set; }
        public int? NewDepartmentId { get; set; }
        public string? OldDepartmentName { get; set; } // 非数据库字段，用于显示
        public string? NewDepartmentName { get; set; } // 非数据库字段，用于显示
        public int? OldPositionId { get; set; }
        public int? NewPositionId { get; set; }
        public string? OldPositionName { get; set; } // 非数据库字段，用于显示
        public string? NewPositionName { get; set; } // 非数据库字段，用于显示
        public string? Reason { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
} 