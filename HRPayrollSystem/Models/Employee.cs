﻿using System;

namespace HRPayrollSystem.Models
{
    public record Employee
    {
        public int Id { get; set; }
        public string? EmployeeNumber { get; set; }
        public string? Name { get; set; }
        public string? Gender { get; set; }
        public string? IdCard { get; set; }
        public DateTime? Birthday { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public int? DepartmentId { get; set; }
        public string? Department { get; set; }
        public int? PositionId { get; set; }
        public string? Position { get; set; }
        public DateTime? HireDate { get; set; }
        public DateTime? ProbationEndDate { get; set; }
        public string? Status { get; set; }
        public decimal BaseSalary { get; set; }
        public DateTime? ResignationDate { get; set; }
        public string? ResignationReason { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}