using System;

namespace HRPayrollSystem.Models
{
    public record Attendance
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public string? EmployeeName { get; set; } // 非数据库字段，用于显示
        public string? EmployeeNumber { get; set; } // 非数据库字段，用于显示
        public string? Department { get; set; } // 非数据库字段，用于显示
        public string? Position { get; set; } // 非数据库字段，用于显示
        public DateTime Date { get; set; }
        public string Status { get; set; } = ""; // 考勤状态：正常, 迟到, 早退, 旷工, 请假
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
} 