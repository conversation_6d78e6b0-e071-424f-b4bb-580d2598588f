using System;

namespace HRPayrollSystem.Models
{
    public record RewardPunishment
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public string? EmployeeName { get; set; }
        public string? EmployeeNumber { get; set; }
        public string Type { get; set; } = ""; // "奖励" 或 "处罚"
        public decimal Amount { get; set; }
        public string? Reason { get; set; }
        public DateTime RecordDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
} 