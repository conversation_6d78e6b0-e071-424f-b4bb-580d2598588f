using System;

namespace HRPayrollSystem.Models
{
    public record Payroll
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public string? EmployeeName { get; set; } // 非数据库字段，用于显示
        public string? EmployeeNumber { get; set; } // 非数据库字段，用于显示
        public string? Department { get; set; } // 非数据库字段，用于显示
        public string? Position { get; set; } // 非数据库字段，用于显示
        public string PayMonth { get; set; } = ""; // 格式: YYYY-MM
        public decimal BaseSalary { get; set; } // 基本工资
        public decimal RewardAmount { get; set; } // 当月奖励总额
        public decimal PenaltyAmount { get; set; } // 当月处罚总额
        public decimal AttendanceDeduction { get; set; } // 考勤扣款
        public decimal Tax { get; set; } // 个人所得税
        public decimal NetSalary { get; set; } // 实发工资
        public string Status { get; set; } = "待确认"; // 状态 (建议值: 待确认, 已发放)
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
} 