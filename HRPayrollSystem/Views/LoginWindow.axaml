<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:HRPayrollSystem.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="320" d:DesignHeight="350"
        x:Class="HRPayrollSystem.Views.LoginWindow"
        x:DataType="vm:LoginWindowViewModel"
        Title="HR人事薪资管理系统 - 登录"
        Width="320" Height="350"
        WindowStartupLocation="CenterScreen"
        TransparencyLevelHint="AcrylicBlur"
        ExtendClientAreaToDecorationsHint="True"
        CanResize="False">
    <Panel>
        <Grid RowDefinitions="Auto,Auto,Auto,*" ColumnDefinitions="Auto,*" Margin="40">
            <TextBlock Text=" "
                       FontSize="24"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Grid.ColumnSpan="2"
                       Margin="0,10,0,20" />

            <TextBox Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                     Grid.Row="1"
                     Grid.Column="1"
                     Margin="0,5"
                     Height="40"
                     BorderThickness="0"
                     HorizontalContentAlignment="Center"
                     VerticalContentAlignment="Center"
                     Watermark="请输入员工工号" />

            <TextBox Text="{Binding Password, UpdateSourceTrigger=PropertyChanged}"
                     PasswordChar="*"
                     Grid.Row="2"
                     Grid.Column="1"
                     Margin="0,5"
                     Height="40"
                     BorderThickness="0"
                     HorizontalContentAlignment="Center"
                     VerticalContentAlignment="Center"
                     Watermark="请输入登录密码" />


            <Button Content="{Binding LoginButtonText}" 
                    BorderThickness="0"
                    Classes="accent"
                    Command="{Binding LoginCommand}"
                    IsEnabled="{Binding !IsLoading}"
                    Grid.Row="3"
                    Grid.Column="1"
                    HorizontalAlignment="Stretch"
                    Margin="0,0,0,15"
                    Height="40" />
        </Grid>
    </Panel>
    <Window.Background>
        <ImageBrush Source="avares://HRPayrollSystem/Assets/1752565941836.png"
                    Stretch="UniformToFill"/>
    </Window.Background>
    
</Window>