using System;
using System.Linq;
using Avalonia.Controls;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.ViewModels;

namespace HRPayrollSystem.Views;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        // 监听 DataContext 变化，当设置后再加载菜单
        this.DataContextChanged += OnDataContextChanged;
    }

    private void OnDataContextChanged(object? sender, EventArgs e)
    {
        LoadMenuItems();
    }

    private void LoadMenuItems()
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            // 清空现有菜单项
            NavView.MenuItems.Clear();

            // 根据用户角色动态添加菜单项
            foreach (var menuItem in viewModel.MenuItems)
            {
                var navItem = new NavigationViewItem
                {
                    Content = menuItem.Title,
                    Tag = menuItem.Tag,
                    IconSource = new SymbolIconSource { Symbol = GetSymbol(menuItem.IconSource) }
                };

                NavView.MenuItems.Add(navItem);
            }

            // 如果有菜单项，默认选择第一个
            if (NavView.MenuItems.Count > 0)
            {
                NavView.SelectedItem = NavView.MenuItems[0];
            }
        }
    }

    private Symbol GetSymbol(string iconSource)
    {
        return iconSource switch
        {
            "Calculator" => Symbol.Calculator,
            "ChartMultiple" => Symbol.Pictures,
            "ContactInfo" => Symbol.ContactInfo,
            "Link" => Symbol.Link,
            "Tag" => Symbol.Tag,
            "People" => Symbol.People,
            "Star" => Symbol.Star,
            "Calendar" => Symbol.Calendar,
            "DockBottom" => Symbol.DockBottom,
            "Settings" => Symbol.Setting,
            _ => Symbol.Home
        };
    }

    public void OnNavItemInvoked(object sender, NavigationViewItemInvokedEventArgs args)
    { 
        if (args.InvokedItemContainer is NavigationViewItem item)
        {
            // 从 Tag 属性获取页面的类型字符串
            var tag = item.Tag?.ToString();
            if (tag != null)
            {
                // 使用反射从字符串获取类型
                var pageType = Type.GetType(tag);
                if (pageType != null)
                {
                    // 使用 Frame 导航到新页面
                    MainFrame.Navigate(pageType);
                }
            }
        }
    }
}