<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Pages"
             xmlns:ui="clr-namespace:FluentAvalonia.UI.Controls;assembly=FluentAvalonia"
             mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="600"
             x:Class="HRPayrollSystem.Views.Pages.PersonalSettingPage"
             x:DataType="vm:PersonalSettingPageViewModel">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <StackPanel Grid.Row="0" Spacing="10">
            <TextBlock Text="个人设置"
                       FontSize="24"
                       FontWeight="Bold" />
            <TextBlock Text="{CompiledBinding CurrentUserInfo}"
                       FontSize="14"
                       Foreground="Gray" />
        </StackPanel>

        <!-- 设置选项 -->
        <StackPanel Grid.Row="1"
                    Spacing="10"
                    HorizontalAlignment="Stretch"
                    Margin="0,30,0,0">

            <ui:SettingsExpander Header="个人信息"
                                 Description="修改个人基本信息，包括姓名、邮箱、联系方式等">

                <ui:SettingsExpander.IconSource>
                    <ui:SymbolIconSource Symbol="ContactInfoFilled" />
                </ui:SettingsExpander.IconSource>

                <ui:SettingsExpander.Footer>
                    <Button Content="修改个人信息"
                            Width="130"
                            HorizontalAlignment="Left"
                            Padding="20,8"
                            CornerRadius="4"
                            Command="{CompiledBinding EditPersonalInfoCommand}" />

                </ui:SettingsExpander.Footer>

            </ui:SettingsExpander>

            <ui:SettingsExpander Header="密码安全"
                                 Description="修改登录密码，确保账户安全">

                <ui:SettingsExpander.IconSource>
                    <ui:SymbolIconSource Symbol="Admin" />
                </ui:SettingsExpander.IconSource>

                <ui:SettingsExpander.Footer>
                    <Button Content="修改密码"
                            Width="130"
                            HorizontalAlignment="Left"
                            Padding="20,8"
                            CornerRadius="4"
                            Command="{CompiledBinding ChangePasswordCommand}" />
                </ui:SettingsExpander.Footer>

            </ui:SettingsExpander>

            <ui:SettingsExpander Header="退出登录"
                                 Description="退出当前账户，返回登录界面">

                <ui:SettingsExpander.IconSource>
                    <ui:SymbolIconSource Symbol="Back" />
                </ui:SettingsExpander.IconSource>

                <ui:SettingsExpander.Footer>
                    <Button Content="退出登录"
                            Width="130"
                            HorizontalAlignment="Left"
                            Padding="20,8"
                            CornerRadius="4"
                            Command="{CompiledBinding LogoutCommand}" />
                </ui:SettingsExpander.Footer>

            </ui:SettingsExpander>

        </StackPanel>

    </Grid>

</UserControl>