<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Pages"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
             x:Class="HRPayrollSystem.Views.Pages.MyPayrollPage"
             x:DataType="vm:MyPayrollPageViewModel">
  <Grid RowDefinitions="Auto,Auto,*,Auto" Margin="20">
    
    <Border Grid.Row="1" 
            Background="{DynamicResource CardBackgroundFillColorDefault}"
            BorderBrush="{DynamicResource CardStrokeColorDefault}"
            BorderThickness="1"
            CornerRadius="4"
            Padding="16"
            Margin="0,0,0,16">
      <Grid ColumnDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,*">
        
        <!-- 起始时间 -->
        <TextBlock Grid.Column="0" Text="起始时间:" VerticalAlignment="Center" Margin="0,0,8,0"/>
        <ComboBox Grid.Column="1" 
                  ItemsSource="{Binding Years}"
                  SelectedItem="{Binding StartYear}"
                  Width="80"
                  Margin="0,0,4,0"
                  VerticalAlignment="Center"/>
        <TextBlock Grid.Column="2" Text="年" VerticalAlignment="Center" Margin="0,0,4,0"/>
        <ComboBox Grid.Column="3" 
                  ItemsSource="{Binding Months}"
                  SelectedItem="{Binding StartMonth}"
                  Width="60"
                  Margin="0,0,4,0"
                  VerticalAlignment="Center"/>
        <TextBlock Grid.Column="4" Text="月" VerticalAlignment="Center" Margin="0,0,16,0"/>
        
        <!-- 结束时间 -->
        <TextBlock Grid.Column="5" Text="到" VerticalAlignment="Center" Margin="0,0,8,0"/>
        <ComboBox Grid.Column="6" 
                  ItemsSource="{Binding Years}"
                  SelectedItem="{Binding EndYear}"
                  Width="80"
                  Margin="0,0,4,0"
                  VerticalAlignment="Center"/>
        <TextBlock Grid.Column="7" Text="年" VerticalAlignment="Center" Margin="0,0,4,0"/>
        <ComboBox Grid.Column="8" 
                  ItemsSource="{Binding Months}"
                  SelectedItem="{Binding EndMonth}"
                  Width="60"
                  Margin="0,0,4,0"
                  VerticalAlignment="Center"/>
        <TextBlock Grid.Column="9" Text="月" VerticalAlignment="Center" Margin="0,0,16,0"/>
        
        <!-- 刷新按钮 -->
        <Button Grid.Column="10" 
                Command="{Binding RefreshCommand}" 
                VerticalAlignment="Center"
                HorizontalAlignment="Right">
          <StackPanel Orientation="Horizontal" Spacing="4">
            <PathIcon Data="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
                      Width="14" Height="14" />
            <TextBlock Text="刷新" />
          </StackPanel>
        </Button>
        
      </Grid>
    </Border>
    
    <!-- 工资单列表 -->
    <DataGrid Grid.Row="2" 
              ItemsSource="{Binding MyPayrolls}"
              AutoGenerateColumns="False"
              CanUserResizeColumns="True"
              IsReadOnly="True"
              VerticalScrollBarVisibility="Auto"
              GridLinesVisibility="Horizontal"
              HeadersVisibility="Column">
      <DataGrid.Columns>
        <DataGridTextColumn Header="工资月份" Binding="{Binding PayMonth}" Width="100"/>
        <DataGridTextColumn Header="基本工资" Binding="{Binding BaseSalary, StringFormat=¥{0:F2}}" Width="100"/>
        <DataGridTextColumn Header="奖励金额" Binding="{Binding RewardAmount, StringFormat=¥{0:F2}}" Width="100"/>
        <DataGridTextColumn Header="处罚金额" Binding="{Binding PenaltyAmount, StringFormat=¥{0:F2}}" Width="100"/>
        <DataGridTextColumn Header="考勤扣款" Binding="{Binding AttendanceDeduction, StringFormat=¥{0:F2}}" Width="100"/>
        <DataGridTextColumn Header="实发工资" Binding="{Binding NetSalary, StringFormat=¥{0:F2}}" Width="100"/>
        <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
        <DataGridTemplateColumn Header="操作" Width="80">
          <DataGridTemplateColumn.CellTemplate>
            <DataTemplate>
              <Button Content="详情" 
                      Command="{Binding $parent[DataGrid].((vm:MyPayrollPageViewModel)DataContext).ViewDetailsCommand}"
                      CommandParameter="{Binding}"
                      Padding="8,4"
                      FontSize="12"/>
            </DataTemplate>
          </DataGridTemplateColumn.CellTemplate>
        </DataGridTemplateColumn>
      </DataGrid.Columns>
    </DataGrid>
    
    <!-- 分页控件 -->
    <Border Grid.Row="3"
            Background="{DynamicResource CardBackgroundFillColorDefault}"
            BorderBrush="{DynamicResource CardStrokeColorDefault}"
            BorderThickness="1"
            CornerRadius="4"
            Padding="16,12"
            Margin="0,16,0,0">
      <Grid ColumnDefinitions="Auto,*,Auto,Auto">
        
        <!-- 分页信息 -->
        <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center">
          <TextBlock Text="共" />
          <TextBlock Text="{Binding TotalItems}" FontWeight="Bold" />
          <TextBlock Text="条记录" />
          <TextBlock Text="，第" />
          <TextBlock Text="{Binding CurrentPage}" FontWeight="Bold" />
          <TextBlock Text="/" />
          <TextBlock Text="{Binding TotalPages}" FontWeight="Bold" />
          <TextBlock Text="页" />
        </StackPanel>

        <!-- 每页显示数量 -->
        <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center" Margin="16,0">
          <TextBlock Text="每页显示" VerticalAlignment="Center" />
          <ComboBox ItemsSource="{Binding PageSizeOptions}"
                    SelectedItem="{Binding PageSize, Mode=TwoWay}"
                    Width="80" />
          <TextBlock Text="条" VerticalAlignment="Center" />
        </StackPanel>

        <!-- 分页导航 -->
        <StackPanel Grid.Column="3" Orientation="Horizontal" Spacing="4" VerticalAlignment="Center">
          
          <!-- 首页按钮 -->
          <Button Command="{Binding FirstPageCommand}"
                  IsEnabled="{Binding !IsFirstPage}"
                  ToolTip.Tip="首页">
            <PathIcon Data="M18.41,16.59L13.82,12L18.41,7.41L17,6L11,12L17,18L18.41,16.59M6,6H8V18H6V6Z"
                      Width="14" Height="14" />
          </Button>

          <!-- 上一页按钮 -->
          <Button Command="{Binding PreviousPageCommand}"
                  IsEnabled="{Binding !IsFirstPage}"
                  ToolTip.Tip="上一页">
            <PathIcon Data="M15.41,16.59L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.59Z"
                      Width="14" Height="14" />
          </Button>

          <!-- 页码输入 -->
          <StackPanel Orientation="Horizontal" Spacing="4" VerticalAlignment="Center">
            <TextBlock Text="转到" VerticalAlignment="Center" />
            <TextBox Text="{Binding GoToPageText, Mode=TwoWay}"
                     Width="50"
                     HorizontalContentAlignment="Center"
                     KeyUp="GoToPageTextBox_KeyUp" />
            <TextBlock Text="页" VerticalAlignment="Center" />
            <Button Command="{Binding GoToPageCommand}"
                    Content="GO"
                    Padding="8,4" />
          </StackPanel>

          <!-- 下一页按钮 -->
          <Button Command="{Binding NextPageCommand}"
                  IsEnabled="{Binding !IsLastPage}"
                  ToolTip.Tip="下一页">
            <PathIcon Data="M8.59,16.59L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.59Z"
                      Width="14" Height="14" />
          </Button>

          <!-- 末页按钮 -->
          <Button Command="{Binding LastPageCommand}"
                  IsEnabled="{Binding !IsLastPage}"
                  ToolTip.Tip="末页">
            <PathIcon Data="M5.59,7.41L10.18,12L5.59,16.59L7,18L13,12L7,6L5.59,7.41M16,6H18V18H16V6Z"
                      Width="14" Height="14" />
          </Button>
          
        </StackPanel>
      </Grid>
    </Border>
    
  </Grid>
</UserControl>
