<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Pages"
             xmlns:ui="clr-namespace:FluentAvalonia.UI.Controls;assembly=FluentAvalonia"
             mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="600"
             x:Class="HRPayrollSystem.Views.Pages.PositionManagementPage"
             x:DataType="vm:PositionManagementPageViewModel"
             x:CompileBindings="False">

    <Grid RowDefinitions="Auto,*,Auto">
        <!-- 顶部操作栏 -->
        <Border Grid.Row="0"
                Background="{DynamicResource CardBackgroundFillColorDefault}"
                BorderBrush="{DynamicResource CardStrokeColorDefault}"
                BorderThickness="0,0,0,1"
                Padding="16,12">
            <Grid ColumnDefinitions="Auto,Auto,*,Auto">
                <!-- 新增岗位按钮 -->
                <Button Command="{Binding ShowAddPositionDialogCommand}"
                        Grid.Column="0"
                        Name="AddPositionButton"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <PathIcon Data="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"
                                  Width="16" Height="16" />
                        <TextBlock Text="新增岗位" />
                    </StackPanel>
                </Button>

                <!-- 刷新按钮 -->
                <Button Command="{Binding RefreshCommand}"
                        Grid.Column="1"
                        Name="RefreshButton"
                        Margin="0,0,16,0">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <PathIcon
                            Data="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
                            Width="16" Height="16" />
                        <TextBlock Text="刷新" />
                    </StackPanel>
                </Button>

                <!-- 搜索框 -->
                <TextBox Grid.Column="3"
                         Name="SearchTextBox"
                         Watermark="搜索岗位名称..."
                         Width="250"
                         VerticalAlignment="Center"
                         Text="{Binding SearchText, Mode=TwoWay}"
                         KeyUp="SearchTextBox_KeyUp">
                    <TextBox.InnerLeftContent>
                        <PathIcon
                            Data="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
                            Width="16" Height="16"
                            Margin="8,0,0,0" />
                    </TextBox.InnerLeftContent>
                </TextBox>
            </Grid>
        </Border>

        <!-- 数据表格 -->
        <DataGrid Grid.Row="1"
                  Name="PositionsDataGrid"
                  ItemsSource="{Binding Positions}"
                  AutoGenerateColumns="False"
                  CanUserReorderColumns="True"
                  CanUserResizeColumns="True"
                  CanUserSortColumns="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  IsReadOnly="False"
                  SelectionMode="Single"
                  Margin="16">
            <DataGrid.Columns>
                <!-- 岗位名称列 -->
                <DataGridTextColumn Header="岗位名称"
                                    Binding="{Binding Name}"
                                    Width="200"
                                    IsReadOnly="True" />

                <!-- 岗位描述列 -->
                <DataGridTextColumn Header="岗位描述"
                                    Binding="{Binding Description}"
                                    Width="300"
                                    IsReadOnly="True" />

                <!-- 创建时间列 -->
                <DataGridTextColumn Header="创建时间"
                                    Binding="{Binding CreatedAt, StringFormat={}{0:yyyy-MM-dd}}"
                                    Width="150"
                                    IsReadOnly="True" />
                
                <!-- 修改时间列 -->
                <DataGridTextColumn Header="修改时间"
                                    Binding="{Binding UpdatedAt, StringFormat={}{0:yyyy-MM-dd}}"
                                    Width="150"
                                    IsReadOnly="True" />

                <!-- 操作列 -->
                <DataGridTemplateColumn Header="操作" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" Spacing="8"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center">
                                <Button Command="{Binding $parent[UserControl].DataContext.EditPositionCommand}"
                                        CommandParameter="{Binding}"
                                        ToolTip.Tip="编辑岗位">
                                    <PathIcon
                                        Data="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"
                                        Width="16" Height="16" />
                                </Button>

                                <Button Command="{Binding $parent[UserControl].DataContext.DeletePositionCommand}"
                                        CommandParameter="{Binding}"
                                        ToolTip.Tip="删除岗位">
                                    <PathIcon
                                        Data="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"
                                        Width="16" Height="16"
                                        Foreground="{DynamicResource SystemFillColorCriticalBrush}" />
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 分页控件 -->
        <Border Grid.Row="2"
                Background="{DynamicResource CardBackgroundFillColorDefault}"
                BorderBrush="{DynamicResource CardStrokeColorDefault}"
                BorderThickness="0,1,0,0"
                Padding="16,12">
            <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                <!-- 分页信息 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center">
                    <TextBlock Text="共" />
                    <TextBlock Text="{Binding TotalItems}" FontWeight="Bold" />
                    <TextBlock Text="条记录" />
                    <TextBlock Text="，第" />
                    <TextBlock Text="{Binding CurrentPage}" FontWeight="Bold" />
                    <TextBlock Text="/" />
                    <TextBlock Text="{Binding TotalPages}" FontWeight="Bold" />
                    <TextBlock Text="页" />
                </StackPanel>

                <!-- 每页显示数量 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center" Margin="16,0">
                    <TextBlock Text="每页显示" VerticalAlignment="Center" />
                    <ComboBox ItemsSource="{CompiledBinding PageSizeOptions}"
                              SelectedItem="{CompiledBinding PageSize, Mode=TwoWay}"
                              Width="80" />
                    <TextBlock Text="条" VerticalAlignment="Center" />
                </StackPanel>

                <!-- 分页导航 -->
                <StackPanel Grid.Column="3" Orientation="Horizontal" Spacing="4" VerticalAlignment="Center">
                    <!-- 首页按钮 -->
                    <Button Command="{Binding FirstPageCommand}"
                            IsEnabled="{Binding !IsFirstPage}"
                            ToolTip.Tip="首页">
                        <PathIcon Data="M18.41,16.59L13.82,12L18.41,7.41L17,6L11,12L17,18L18.41,16.59M6,6H8V18H6V6Z"
                                  Width="14" Height="14" />
                    </Button>

                    <!-- 上一页按钮 -->
                    <Button Command="{Binding PreviousPageCommand}"
                            IsEnabled="{Binding !IsFirstPage}"
                            ToolTip.Tip="上一页">
                        <PathIcon Data="M15.41,16.59L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.59Z"
                                  Width="14" Height="14" />
                    </Button>

                    <!-- 页码输入 -->
                    <StackPanel Orientation="Horizontal" Spacing="4" VerticalAlignment="Center">
                        <TextBlock Text="转到" VerticalAlignment="Center" />
                        <TextBox Text="{Binding GoToPageText, Mode=TwoWay}"
                                 Width="50"
                                 HorizontalContentAlignment="Center"
                                 KeyUp="GoToPageTextBox_KeyUp" 
                                 />
                        <TextBlock Text="页" VerticalAlignment="Center" />
                        <Button Command="{Binding GoToPageCommand}"
                                Content="GO"
                                Padding="8,4" />
                    </StackPanel>

                    <!-- 下一页按钮 -->
                    <Button Command="{Binding NextPageCommand}"
                            IsEnabled="{Binding !IsLastPage}"
                            ToolTip.Tip="下一页">
                        <PathIcon Data="M8.59,16.59L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.59Z"
                                  Width="14" Height="14" />
                    </Button>

                    <!-- 末页按钮 -->
                    <Button Command="{Binding LastPageCommand}"
                            IsEnabled="{Binding !IsLastPage}"
                            ToolTip.Tip="末页">
                        <PathIcon Data="M5.59,7.41L10.18,12L5.59,16.59L7,18L13,12L7,6L5.59,7.41M16,6H18V18H16V6Z"
                                  Width="14" Height="14" />
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
    
</UserControl> 