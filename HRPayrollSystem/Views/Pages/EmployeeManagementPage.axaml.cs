﻿using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.ViewModels.Pages;
using HRPayrollSystem.Views.Dialogs;
using System.Windows.Input;

namespace HRPayrollSystem.Views.Pages;

public partial class EmployeeManagementPage : UserControl
{
    public EmployeeManagementPage()
    {
        InitializeComponent();
        
        // 从依赖注入容器获取服务
        var employeeManagementService = ServiceProvider.GetService<EmployeeManagementService>();
        var departmentManagementService = ServiceProvider.GetService<DepartmentManagementService>();
        var positionManagementService = ServiceProvider.GetService<PositionManagementService>();
        var transferService = ServiceProvider.GetService<TransferService>();
        
        DataContext = new EmployeeManagementPageViewModel(
            employeeManagementService, 
            departmentManagementService,
            positionManagementService,
            transferService);
    }
    
    private void SearchTextBox_OnKeyUp(object? sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is EmployeeManagementPageViewModel vm)
        {
            if (vm.SearchCommand.CanExecute(null))
                vm.SearchCommand.Execute(null);
        }
    }

    private void EmployeesDataGrid_OnDoubleTapped(object? sender, TappedEventArgs e)
    {
        if (sender is DataGrid dataGrid && dataGrid.SelectedItem is Employee selectedEmployee && 
            DataContext is EmployeeManagementPageViewModel viewModel)
        {
            if (viewModel.ShowEmployeeDetailsCommand.CanExecute(selectedEmployee))
            {
                viewModel.ShowEmployeeDetailsCommand.Execute(selectedEmployee);
            }
        }
    }
    
    private void GoToPageTextBox_KeyUp(object? sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is EmployeeManagementPageViewModel vm)
        {
            if (vm.GoToPageCommand != null && vm.GoToPageCommand.CanExecute(null))
                vm.GoToPageCommand.Execute(null);
        }
    }
}