using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Markup.Xaml;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Pages;

namespace HRPayrollSystem.Views.Pages
{
    public partial class AttendanceManagementPage : UserControl
    {
        public AttendanceManagementPage()
        {
            InitializeComponent();
            
            // 从依赖注入容器获取服务
            var attendanceService = ServiceProvider.GetService<AttendanceManagementService>();
            var departmentService = ServiceProvider.GetService<DepartmentManagementService>();
            var employeeService = ServiceProvider.GetService<EmployeeManagementService>();
            var userService = ServiceProvider.GetService<UserManagementService>();
            var rewardPunishmentService = ServiceProvider.GetService<RewardPunishmentManagementService>();
            
            DataContext = new AttendanceManagementPageViewModel(attendanceService, departmentService, employeeService, userService, rewardPunishmentService);
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }
        
        private void SearchTextBox_KeyUp(object? sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && DataContext is AttendanceManagementPageViewModel viewModel)
            {
                if (viewModel.SearchCommand.CanExecute(null))
                    viewModel.SearchCommand.Execute(null);
            }
        }
        
        private void AttendancesDataGrid_OnDoubleTapped(object? sender, TappedEventArgs e)
        {
            if (DataContext is AttendanceManagementPageViewModel viewModel && 
                sender is DataGrid dataGrid && 
                dataGrid.SelectedItem is AttendanceSummary summary)
            {
                if (viewModel.ShowAttendanceDetailsCommand.CanExecute(summary))
                    viewModel.ShowAttendanceDetailsCommand.Execute(summary);
            }
        }
        
        private void GoToPageTextBox_KeyUp(object? sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && DataContext is AttendanceManagementPageViewModel vm)
            {
                if (vm.GoToPageCommand != null && vm.GoToPageCommand.CanExecute(null))
                    vm.GoToPageCommand.Execute(null);
            }
        }
    }
} 