using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Pages;

namespace HRPayrollSystem.Views.Pages
{
    public partial class PayrollManagementPage : UserControl
    {
        public PayrollManagementPage()
        {
            InitializeComponent();
            
            // 从依赖注入容器获取服务
            var payrollManagementService = ServiceProvider.GetService<PayrollManagementService>();
            var rewardPunishmentService = ServiceProvider.GetService<RewardPunishmentManagementService>();
            var employeeManagementService = ServiceProvider.GetService<EmployeeManagementService>();
            var attendanceManagementService = ServiceProvider.GetService<AttendanceManagementService>();
            
            DataContext = new PayrollManagementPageViewModel(
                payrollManagementService,
                rewardPunishmentService,
                employeeManagementService,
                attendanceManagementService);
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }
        
        private void SearchTextBox_OnKeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && DataContext is PayrollManagementPageViewModel viewModel)
            {
                viewModel.SearchCommand.Execute(null);
            }
        }
        
        private void GoToPageTextBox_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && DataContext is PayrollManagementPageViewModel viewModel)
            {
                viewModel.GoToPageCommand.Execute(null);
            }
        }
        
        private void PayrollsDataGrid_OnDoubleTapped(object sender, RoutedEventArgs e)
        {
            if (DataContext is PayrollManagementPageViewModel viewModel && 
                viewModel.SelectedPayroll != null)
            {
                viewModel.ShowDetailsCommand.Execute(viewModel.SelectedPayroll);
            }
        }
    }
} 