using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.ViewModels.Pages;
using HRPayrollSystem.Views.Dialogs;
using System.Windows.Input;

namespace HRPayrollSystem.Views.Pages;

public partial class UserManagementPage : UserControl
{
    public UserManagementPage()
    {
        InitializeComponent();
        
        // 从依赖注入容器获取服务
        var userManagementService = ServiceProvider.GetService<UserManagementService>();
        
        DataContext = new UserManagementPageViewModel(userManagementService);
    }

    private void SearchTextBox_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is UserManagementPageViewModel vm)
        {
            if (vm.SearchCommand.CanExecute(null))
                vm.SearchCommand.Execute(null);
        }
    }

    private void GoToPageTextBox_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is UserManagementPageViewModel vm)
        {
            if (vm.GoToPageCommand.CanExecute(null))
                vm.GoToPageCommand.Execute(null);
        }
    }
}