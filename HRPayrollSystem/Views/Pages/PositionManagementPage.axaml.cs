using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Pages;
using Avalonia.Input;
using System.Windows.Input;

namespace HRPayrollSystem.Views.Pages;

public partial class PositionManagementPage : UserControl
{
    public PositionManagementPage()
    {
        // 从依赖注入容器获取服务
        var positionManagementService = ServiceProvider.GetService<PositionManagementService>();
        
        DataContext = new PositionManagementPageViewModel(positionManagementService);
        InitializeComponent();
    }

    private void SearchTextBox_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is PositionManagementPageViewModel vm)
        {
            if (vm.SearchCommand.CanExecute(null))
                vm.SearchCommand.Execute(null);
        }
    }

    private void GoToPageTextBox_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is PositionManagementPageViewModel vm)
        {
            if (vm.GoToPageCommand.CanExecute(null))
                vm.GoToPageCommand.Execute(null);
        }
    }
}