<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Pages"
             xmlns:lvc="using:LiveChartsCore.SkiaSharpView.Avalonia"
             xmlns:ui="using:FluentAvalonia.UI.Controls"
             xmlns:ic="using:FluentIcons.Avalonia"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="HRPayrollSystem.Views.Pages.DataAnalysisPage"
             x:DataType="vm:DataAnalysisPageViewModel">

    <Design.DataContext>
        <vm:DataAnalysisPageViewModel />
    </Design.DataContext>

    <ScrollViewer x:Name="RootScroll">
        <StackPanel Spacing="20" Margin="20">
            <TextBlock Text="数据分析报表" FontSize="28" FontWeight="Bold" Margin="0,0,0,20"/>

            <!-- 部门人员分布饼图 -->
            <Border Background="White" CornerRadius="8" Padding="20" BoxShadow="0 2 8 0 #20000000">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="400"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="部门人员分布" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        <Button Grid.Column="1"
                                Command="{Binding ExportDepartmentDistributionToCSVCommand}"
                                Classes="accent">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <ic:FluentIcon Icon="DocumentArrowDown"/>
                                    <TextBlock Text="导出CSV"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </Grid>

                    <TextBlock Grid.Row="1" Text="展示各部门员工数量分布情况" 
                               FontSize="14" Foreground="Gray" Margin="0,0,0,15"/>

                    <lvc:PieChart Grid.Row="2" Series="{Binding DepartmentDistributionChart}" 
                                  LegendPosition="Right"/>
                </Grid>
            </Border>

            <!-- 部门平均薪资柱状图 -->
            <Border Background="White" CornerRadius="8" Padding="20" BoxShadow="0 2 8 0 #20000000">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="400"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="部门平均薪资对比" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        <Button Grid.Column="1"
                                Command="{Binding ExportSalaryTrendToCSVCommand}"
                                Classes="accent">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <ic:FluentIcon Icon="DocumentArrowDown" />
                                    <TextBlock Text="导出CSV"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </Grid>

                    <TextBlock Grid.Row="1" Text="对比各部门员工的平均薪资水平" 
                               FontSize="14" Foreground="Gray" Margin="0,0,0,15"/>

                    <lvc:CartesianChart Grid.Row="2" Series="{Binding SalaryTrendChart}"
                                        PointerWheelChanged="Chart_OnPointerWheelChanged"
                    ZoomMode="None" />
                </Grid>
            </Border>

            <!-- 考勤状态分布饼图 -->
            <Border Background="White" CornerRadius="8" Padding="20" BoxShadow="0 2 8 0 #20000000">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="400"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="考勤状态分布" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        <Button Grid.Column="1" 
                                Command="{Binding ExportAttendanceStatusToCSVCommand}"
                                Classes="accent">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <ic:FluentIcon Icon="DocumentArrowDown" />
                                    <TextBlock Text="导出CSV"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </Grid>

                    <TextBlock Grid.Row="1" Text="展示员工考勤状态的分布情况" 
                               FontSize="14" Foreground="Gray" Margin="0,0,0,15"/>

                    <lvc:PieChart Grid.Row="2" Series="{Binding AttendanceStatusChart}" 
                                  LegendPosition="Right"/>
                </Grid>
            </Border>

            <!-- 奖惩统计图 -->
            <Border Background="White" CornerRadius="8" Padding="20" BoxShadow="0 2 8 0 #20000000">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="400"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="奖惩统计" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        <Button Grid.Column="1" 
                                Command="{Binding ExportRewardPunishmentToCSVCommand}"
                                Classes="accent">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <ic:FluentIcon Icon="DocumentArrowDown" />
                                    <TextBlock Text="导出CSV"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </Grid>

                    <TextBlock Grid.Row="1" Text="展示奖励和处罚的数量及金额统计" 
                               FontSize="14" Foreground="Gray" Margin="0,0,0,15"/>

                    <lvc:CartesianChart Grid.Row="2" Series="{Binding RewardPunishmentChart}"/>
                </Grid>
            </Border>

            <!-- 薪资分布图 -->
            <Border Background="White" CornerRadius="8" Padding="20" BoxShadow="0 2 8 0 #20000000">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="400"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="薪资分布" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        <Button Grid.Column="1"
                                Command="{Binding ExportSalaryRangeToCSVCommand}"
                                Classes="accent">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <ic:FluentIcon Icon="DocumentArrowDown" />
                                    <TextBlock Text="导出CSV"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </Grid>

                    <TextBlock Grid.Row="1" Text="展示不同薪资范围的员工数量分布" 
                               FontSize="14" Foreground="Gray" Margin="0,0,0,15"/>

                    <lvc:CartesianChart Grid.Row="2" Series="{Binding SalaryRangeChart}"/>
                </Grid>
            </Border>

            <!-- 月度薪资支出趋势图 -->
            <Border Background="White" CornerRadius="8" Padding="20" BoxShadow="0 2 8 0 #20000000">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="400"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="月度薪资支出趋势" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        <Button Grid.Column="1" 
                                Command="{Binding ExportMonthlyPayrollToCSVCommand}"
                                Classes="accent">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <ic:FluentIcon Icon="DocumentArrowDown"/>
                                    <TextBlock Text="导出CSV"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </Grid>

                    <TextBlock Grid.Row="1" Text="展示公司月度薪资支出的变化趋势" 
                               FontSize="14" Foreground="Gray" Margin="0,0,0,15"/>

                    <lvc:CartesianChart Grid.Row="2" Series="{Binding MonthlyPayrollChart}"/>
                </Grid>
            </Border>
            <Panel Background="Transparent"/>
        </StackPanel>
        
    </ScrollViewer>
</UserControl>
