using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Markup.Xaml;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Pages;

namespace HRPayrollSystem.Views.Pages
{
    public partial class MyPayrollPage : UserControl
    {
        public MyPayrollPage()
        {
            InitializeComponent();
            
            // 从依赖注入容器获取服务
            var payrollManagementService = ServiceProvider.GetService<PayrollManagementService>();
            var employeeManagementService = ServiceProvider.GetService<EmployeeManagementService>();
            
            DataContext = new MyPayrollPageViewModel(payrollManagementService, employeeManagementService);
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }
        
        private void GoToPageTextBox_KeyUp(object? sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && DataContext is MyPayrollPageViewModel vm)
            {
                if (vm.GoToPageCommand.CanExecute(null))
                    vm.GoToPageCommand.Execute(null);
            }
        }
    }
}
