<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Pages"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
             x:Class="HRPayrollSystem.Views.Pages.AttendanceManagementPage"
             x:DataType="vm:AttendanceManagementPageViewModel"
             x:CompileBindings="False">
  <Grid RowDefinitions="Auto,Auto,*,Auto" Margin="20">
    <!-- 操作按钮 -->
    <StackPanel Grid.Row="0" Orientation="Horizontal" Spacing="10" Margin="0,0,0,15">
      <Button Command="{Binding ImportCommand}" Content="批量导入" HorizontalContentAlignment="Center" Width="90"/>
      <Button Command="{Binding ShowAddAttendanceDialogCommand}" Content="手动录入" HorizontalContentAlignment="Center" Width="90"/>
      <Button Command="{Binding ExportCommand}" Content="导出" HorizontalContentAlignment="Center" Width="90"/>
      <Button Command="{Binding RefreshCommand}" Content="刷新" HorizontalContentAlignment="Center" Width="90"/>
    </StackPanel>
    
    <!-- 筛选栏和搜索栏 -->
    <Grid Grid.Row="1" ColumnDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,*" Margin="0,0,0,15">
      <!-- 日期筛选 -->
      <TextBlock Grid.Column="0" Text="日期:" VerticalAlignment="Center" Margin="0,0,10,0"/>
      <CalendarDatePicker Grid.Column="1" HorizontalAlignment="Stretch" SelectedDate="{Binding StartDate}" Watermark="选择开始日期"/>
      <TextBlock Grid.Column="2" Text="-" VerticalAlignment="Center" Margin="5,0"/>
      <CalendarDatePicker Grid.Column="3" HorizontalAlignment="Stretch" SelectedDate="{Binding EndDate}" Watermark="选择结束日期"/>
      
      <!-- 部门筛选 -->
      <TextBlock Grid.Column="4" Text="部门:" VerticalAlignment="Center" Margin="10,0,10,0"/>
      <ComboBox Grid.Column="5" HorizontalAlignment="Stretch" ItemsSource="{Binding Departments}" SelectedItem="{Binding SelectedDepartment}">
        <ComboBox.ItemTemplate>
          <DataTemplate>
            <TextBlock Text="{Binding Name}"/>
          </DataTemplate>
        </ComboBox.ItemTemplate>
      </ComboBox>
      
      <!-- 搜索框 -->
      <TextBlock Grid.Column="6" Text="搜索:" VerticalAlignment="Center" Margin="10,0,10,0"/>
      <TextBox Grid.Column="7" HorizontalAlignment="Stretch" Text="{Binding SearchText}" Watermark="搜索员工姓名、工号或部门..." KeyUp="SearchTextBox_KeyUp"/>
      
      <!-- 查询按钮 -->
      <Button Grid.Column="8" Command="{Binding SearchCommand}" Content="查询" HorizontalAlignment="Right" Width="90" Margin="10,0,0,0"/>
    </Grid>
    
    <!-- 数据表格 -->
    <DataGrid Grid.Row="2" 
              ItemsSource="{Binding Attendances}" 
              AutoGenerateColumns="False"
              CanUserResizeColumns="True"
              IsReadOnly="True"
              VerticalScrollBarVisibility="Auto"
              Margin="0,0,0,15"
              DoubleTapped="AttendancesDataGrid_OnDoubleTapped">
      <DataGrid.Columns>
        <DataGridTextColumn Header="员工编号" Binding="{Binding EmployeeNumber}" Width="100"/>
        <DataGridTextColumn Header="姓名" Binding="{Binding EmployeeName}" Width="100"/>
        <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="120"/>
        <DataGridTextColumn Header="职务" Binding="{Binding Position}" Width="120"/>
        <DataGridTextColumn Header="正常次数" Binding="{Binding NormalCount}" Width="80"/>
        <DataGridTextColumn Header="迟到次数" Binding="{Binding LateCount}" Width="80"/>
        <DataGridTextColumn Header="早退次数" Binding="{Binding EarlyLeaveCount}" Width="80"/>
        <DataGridTextColumn Header="旷工次数" Binding="{Binding AbsentCount}" Width="80"/>
        <DataGridTextColumn Header="请假次数" Binding="{Binding LeaveCount}" Width="80"/>
      </DataGrid.Columns>
    </DataGrid>
    
    <!-- 分页控件 -->
    <Border Grid.Row="3"
            Background="{DynamicResource CardBackgroundFillColorDefault}"
            BorderBrush="{DynamicResource CardStrokeColorDefault}"
            BorderThickness="1"
            CornerRadius="4"
            Padding="16,12">
        <Grid ColumnDefinitions="Auto,*,Auto,Auto">
            <!-- 分页信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center">
                <TextBlock Text="共" />
                <TextBlock Text="{Binding TotalItems}" FontWeight="Bold" />
                <TextBlock Text="条记录" />
                <TextBlock Text="，第" />
                <TextBlock Text="{Binding CurrentPage}" FontWeight="Bold" />
                <TextBlock Text="/" />
                <TextBlock Text="{Binding TotalPages}" FontWeight="Bold" />
                <TextBlock Text="页" />
            </StackPanel>

            <!-- 每页显示数量 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center" Margin="16,0">
                <TextBlock Text="每页显示" VerticalAlignment="Center" />
                <ComboBox ItemsSource="{Binding PageSizeOptions}"
                          SelectedItem="{Binding PageSize, Mode=TwoWay}"
                          Width="80" />
                <TextBlock Text="条" VerticalAlignment="Center" />
            </StackPanel>

            <!-- 分页导航 -->
            <StackPanel Grid.Column="3" Orientation="Horizontal" Spacing="4" VerticalAlignment="Center">
                <!-- 首页按钮 -->
                <Button Command="{Binding FirstPageCommand}"
                        IsEnabled="{Binding !IsFirstPage}"
                        ToolTip.Tip="首页">
                    <PathIcon Data="M18.41,16.59L13.82,12L18.41,7.41L17,6L11,12L17,18L18.41,16.59M6,6H8V18H6V6Z"
                              Width="14" Height="14" />
                </Button>

                <!-- 上一页按钮 -->
                <Button Command="{Binding PreviousPageCommand}"
                        IsEnabled="{Binding !IsFirstPage}"
                        ToolTip.Tip="上一页">
                    <PathIcon Data="M15.41,16.59L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.59Z"
                              Width="14" Height="14" />
                </Button>

                <!-- 页码输入 -->
                <StackPanel Orientation="Horizontal" Spacing="4" VerticalAlignment="Center">
                    <TextBlock Text="转到" VerticalAlignment="Center" />
                    <TextBox Text="{Binding GoToPageText, Mode=TwoWay}"
                             Width="50"
                             HorizontalContentAlignment="Center"
                             KeyUp="GoToPageTextBox_KeyUp" />
                    <TextBlock Text="页" VerticalAlignment="Center" />
                    <Button Command="{Binding GoToPageCommand}"
                            Content="GO"
                            Padding="8,4" />
                </StackPanel>

                <!-- 下一页按钮 -->
                <Button Command="{Binding NextPageCommand}"
                        IsEnabled="{Binding !IsLastPage}"
                        ToolTip.Tip="下一页">
                    <PathIcon Data="M8.59,16.59L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.59Z"
                              Width="14" Height="14" />
                </Button>

                <!-- 末页按钮 -->
                <Button Command="{Binding LastPageCommand}"
                        IsEnabled="{Binding !IsLastPage}"
                        ToolTip.Tip="末页">
                    <PathIcon Data="M5.59,7.41L10.18,12L5.59,16.59L7,18L13,12L7,6L5.59,7.41M16,6H18V18H16V6Z"
                              Width="14" Height="14" />
                </Button>
            </StackPanel>
        </Grid>
    </Border>
  </Grid>
</UserControl> 