using System.Reactive.Linq;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Input;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Pages;

namespace HRPayrollSystem.Views.Pages;

public partial class RewardPunishmentManagementPage : UserControl
{
    public RewardPunishmentManagementPage()
    {
        InitializeComponent();
        
        // 从依赖注入容器获取服务
        var rewardPunishmentManagementService = ServiceProvider.GetService<RewardPunishmentManagementService>();
        var userManagementService = ServiceProvider.GetService<UserManagementService>();
        
        DataContext = new RewardPunishmentManagementPageViewModel(rewardPunishmentManagementService, userManagementService);
    }

    private async void SearchTextBox_OnKeyUp(object? sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is RewardPunishmentManagementPageViewModel viewModel)
        {
            await viewModel.SearchCommand.Execute();
        }
    }

    private async void RewardPunishmentsDataGrid_OnDoubleTapped(object? sender, TappedEventArgs e)
    {
        if (DataContext is RewardPunishmentManagementPageViewModel viewModel && 
            sender is DataGrid dataGrid && 
            dataGrid.SelectedItem is HRPayrollSystem.Models.RewardPunishment rewardPunishment)
        {
            await viewModel.ShowDetailsCommand.Execute(rewardPunishment);
        }
    }

    private void GoToPageTextBox_KeyUp(object? sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is RewardPunishmentManagementPageViewModel vm)
        {
            if (vm.GoToPageCommand.CanExecute(null))
                vm.GoToPageCommand.Execute(null);
        }
    }
}