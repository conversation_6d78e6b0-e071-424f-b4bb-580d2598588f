using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.VisualTree;
using HRPayrollSystem.ViewModels.Pages;
using LiveChartsCore.SkiaSharpView.Avalonia;

namespace HRPayrollSystem.Views.Pages;

public partial class DataAnalysisPage : UserControl
{
    public DataAnalysisPage()
    {
        InitializeComponent();
        DataContext = new DataAnalysisPageViewModel();
        AddHandler(PointerWheelChangedEvent, OnPreviewWheel, RoutingStrategies.Tunnel);
    }
    private void OnPreviewWheel(object? sender, PointerWheelEventArgs e)
    {
            // 2. 直接标记为未处理，强制继续冒泡
            e.Handled = false;
    }
    private void Chart_OnPointerWheelChanged(object? sender, PointerWheelEventArgs e)
    {
        e.Handled = false;
    }
}