using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Pages;

namespace HRPayrollSystem.Views.Pages;

public partial class PersonalSettingPage : UserControl
{
    public PersonalSettingPage()
    {
        InitializeComponent();
        
        // 从依赖注入容器获取服务
        var userManagementService = ServiceProvider.GetService<UserManagementService>();
        var employeeManagementService = ServiceProvider.GetService<EmployeeManagementService>();
        
        DataContext = new PersonalSettingPageViewModel(userManagementService, employeeManagementService);
    }
}