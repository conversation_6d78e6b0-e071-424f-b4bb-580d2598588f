<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Pages"
             xmlns:ui="clr-namespace:FluentAvalonia.UI.Controls;assembly=FluentAvalonia"
             mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="600"
             x:Class="HRPayrollSystem.Views.Pages.PayrollManagementPage"
             x:DataType="vm:PayrollManagementPageViewModel"
             x:CompileBindings="False">

    <Grid RowDefinitions="Auto,*,Auto">
        <!-- 顶部操作栏 -->
        <Border Grid.Row="0"
                Background="{DynamicResource CardBackgroundFillColorDefault}"
                BorderBrush="{DynamicResource CardStrokeColorDefault}"
                BorderThickness="0,0,0,1"
                Padding="16,12">
            <Grid ColumnDefinitions="Auto,Auto,Auto,*,Auto">
                <!-- 生成工资单按钮 -->
                <Button Command="{Binding GeneratePayrollsCommand}"
                        Grid.Column="0"
                        Name="GeneratePayrollButton"
                        Margin="0,0,8,0"
                        IsEnabled="{Binding !IsGenerating}">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <PathIcon Data="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"
                                  Width="16" Height="16" />
                        <TextBlock Text="生成工资单" />
                    </StackPanel>
                </Button>

                <!-- 批量确认按钮 -->
                <Button Command="{Binding BatchConfirmCommand}"
                        Grid.Column="1"
                        Name="BatchConfirmButton"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <PathIcon Data="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
                                  Width="16" Height="16" />
                        <TextBlock Text="批量确认发放" />
                    </StackPanel>
                </Button>

                <!-- 导出按钮 -->
                <Button Command="{Binding ExportPayrollsCommand}"
                        Grid.Column="2"
                        Name="ExportButton"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <PathIcon Data="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"
                                  Width="16" Height="16" />
                        <TextBlock Text="导出工资单" />
                    </StackPanel>
                </Button>

                <!-- 年月选择器 -->
                <StackPanel Grid.Column="3"
                            Orientation="Horizontal"
                            Spacing="8"
                            Margin="16,0,0,0"
                            VerticalAlignment="Center">
                    <TextBlock Text="起始日期:" VerticalAlignment="Center" />
                    <ComboBox ItemsSource="{Binding Years}"
                              SelectedItem="{Binding StartYear}"
                              Width="100"
                              VerticalAlignment="Center" />
                    <TextBlock Text="年" VerticalAlignment="Center" />
                    <ComboBox ItemsSource="{Binding Months}"
                              SelectedItem="{Binding StartMonth}"
                              Width="80"
                              VerticalAlignment="Center" />
                    <TextBlock Text="月" VerticalAlignment="Center" />

                    <TextBlock Text="到" VerticalAlignment="Center" Margin="8,0" />

                    <ComboBox ItemsSource="{Binding Years}"
                              SelectedItem="{Binding EndYear}"
                              Width="100"
                              VerticalAlignment="Center" />
                    <TextBlock Text="年" VerticalAlignment="Center" />
                    <ComboBox ItemsSource="{Binding Months}"
                              SelectedItem="{Binding EndMonth}"
                              Width="80"
                              VerticalAlignment="Center" />
                    <TextBlock Text="月" VerticalAlignment="Center" />
                </StackPanel>

                <!-- 搜索框 -->
                <TextBox Grid.Column="4"
                         Name="SearchTextBox"
                         Watermark="搜索员工姓名或工号..."
                         Width="250"
                         Text="{Binding SearchText, Mode=TwoWay}"
                         KeyUp="SearchTextBox_OnKeyUp"
                         VerticalAlignment="Center">
                    <TextBox.InnerLeftContent>
                        <PathIcon
                            Data="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
                            Width="16" Height="16"
                            Margin="8,0,0,0" />
                    </TextBox.InnerLeftContent>
                </TextBox>
            </Grid>
        </Border>

        <!-- 数据表格 -->
        <DataGrid Grid.Row="1"
                  Name="PayrollsDataGrid"
                  ItemsSource="{Binding Payrolls}"
                  AutoGenerateColumns="False"
                  CanUserReorderColumns="True"
                  CanUserResizeColumns="True"
                  CanUserSortColumns="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  IsReadOnly="True"
                  SelectionMode="Single"
                  SelectedItem="{Binding SelectedPayroll}"
                  Margin="16"
                  DoubleTapped="PayrollsDataGrid_OnDoubleTapped">
            <DataGrid.Columns>
                <DataGridTextColumn Header="工号"
                                    Binding="{Binding EmployeeNumber}"
                                    Width="100" />
                <DataGridTextColumn Header="姓名"
                                    Binding="{Binding EmployeeName}"
                                    Width="100" />
                <DataGridTextColumn Header="部门"
                                    Binding="{Binding Department}"
                                    Width="120" />
                <DataGridTextColumn Header="职位"
                                    Binding="{Binding Position}"
                                    Width="120" />
                <DataGridTextColumn Header="月份"
                                    Binding="{Binding PayMonth}"
                                    Width="80" />
                <DataGridTextColumn Header="基本工资"
                                    Binding="{Binding BaseSalary, StringFormat={}{0:C2}}"
                                    Width="100" />
                <DataGridTextColumn Header="奖励金额"
                                    Binding="{Binding RewardAmount, StringFormat={}{0:C2}}"
                                    Width="100" />
                <DataGridTextColumn Header="处罚金额"
                                    Binding="{Binding PenaltyAmount, StringFormat={}{0:C2}}"
                                    Width="100" />
                <DataGridTextColumn Header="考勤扣款"
                                    Binding="{Binding AttendanceDeduction, StringFormat={}{0:C2}}"
                                    Width="100" />
                <DataGridTextColumn Header="实发工资"
                                    Binding="{Binding NetSalary, StringFormat={}{0:C2}}"
                                    Width="100" />
                <DataGridTextColumn Header="状态"
                                    Binding="{Binding Status}"
                                    Width="80" />
                <DataGridTemplateColumn Header="操作" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" Spacing="8"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center">
                                <Button
                                    Command="{Binding $parent[UserControl].DataContext.ShowDetailsCommand}"
                                    CommandParameter="{Binding}"
                                    ToolTip.Tip="查看详情">
                                    <PathIcon
                                        Data="M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"
                                        Width="16" Height="16" />
                                </Button>

                                <Button
                                    Command="{Binding $parent[UserControl].DataContext.ConfirmPayrollCommand}"
                                    CommandParameter="{Binding}"
                                    IsEnabled="{Binding Status, Converter={StaticResource StringEqualsConverter}, ConverterParameter=待确认}"
                                    ToolTip.Tip="确认发放工资">
                                    <PathIcon
                                        Data="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
                                        Width="16" Height="16" />
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 分页控件 -->
        <Border Grid.Row="2"
                Background="{DynamicResource CardBackgroundFillColorDefault}"
                BorderBrush="{DynamicResource CardStrokeColorDefault}"
                BorderThickness="0,1,0,0"
                Padding="16,12">
            <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                <!-- 分页信息 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center">
                    <TextBlock Text="共" />
                    <TextBlock Text="{Binding TotalItems}" FontWeight="Bold" />
                    <TextBlock Text="条记录" />
                    <TextBlock Text="，第" />
                    <TextBlock Text="{Binding CurrentPage}" FontWeight="Bold" />
                    <TextBlock Text="/" />
                    <TextBlock Text="{Binding TotalPages}" FontWeight="Bold" />
                    <TextBlock Text="页" />
                </StackPanel>

                <!-- 每页显示数量 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="8" VerticalAlignment="Center"
                            Margin="16,0">
                    <TextBlock Text="每页显示" VerticalAlignment="Center" />
                    <ComboBox ItemsSource="{CompiledBinding PageSizeOptions}"
                              SelectedItem="{CompiledBinding PageSize, Mode=TwoWay}"
                              Width="80" />
                    <TextBlock Text="条" VerticalAlignment="Center" />
                </StackPanel>

                <!-- 分页导航 -->
                <StackPanel Grid.Column="3" Orientation="Horizontal" Spacing="4" VerticalAlignment="Center">
                    <!-- 首页按钮 -->
                    <Button Command="{Binding FirstPageCommand}" Height="32"

                            IsEnabled="{Binding !IsFirstPage}"
                            ToolTip.Tip="首页">
                        <PathIcon Data="M18.41,16.59L13.82,12L18.41,7.41L17,6L11,12L17,18L18.41,16.59M6,6H8V18H6V6Z"
                                  Width="14" Height="14" />
                    </Button>

                    <!-- 上一页按钮 -->
                    <Button Command="{Binding PreviousPageCommand}" Height="32"

                            IsEnabled="{Binding !IsFirstPage}"
                            ToolTip.Tip="上一页">
                        <PathIcon Data="M15.41,16.59L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.59Z"
                                  Width="14" Height="14" />
                    </Button>

                    <!-- 页码输入 -->
                    <StackPanel Orientation="Horizontal" Spacing="4" VerticalAlignment="Center">
                        <TextBlock Text="转到" VerticalAlignment="Center" />
                        <TextBox Text="{Binding GoToPageText, Mode=TwoWay}"
                                 Width="50"
                                 HorizontalContentAlignment="Center"
                                 KeyUp="GoToPageTextBox_KeyUp" />
                        <TextBlock Text="页" VerticalAlignment="Center" />
                        <Button Command="{Binding GoToPageCommand}"
                                Content="GO"
                                Height="32"
                                Padding="8,4" />
                    </StackPanel>

                    <!-- 下一页按钮 -->
                    <Button Command="{Binding NextPageCommand}"
                            IsEnabled="{Binding !IsLastPage}" Height="32"

                            ToolTip.Tip="下一页">
                        <PathIcon Data="M8.59,16.59L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.59Z"
                                  Width="14" Height="14" />
                    </Button>

                    <!-- 末页按钮 -->
                    <Button Command="{Binding LastPageCommand}"
                            IsEnabled="{Binding !IsLastPage}" Height="32"

                            ToolTip.Tip="末页">
                        <PathIcon Data="M5.59,7.41L10.18,12L5.59,16.59L7,18L13,12L7,6L5.59,7.41M16,6H18V18H16V6Z"
                                  Width="14" Height="14" />
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>