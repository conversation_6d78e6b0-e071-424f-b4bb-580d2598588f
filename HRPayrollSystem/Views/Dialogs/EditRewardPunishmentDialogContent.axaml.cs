using Avalonia.Controls;
using Avalonia.Interactivity;
using HRPayrollSystem.ViewModels.Dialogs;

namespace HRPayrollSystem.Views.Dialogs;

public partial class EditRewardPunishmentDialogContent : UserControl
{
    public EditRewardPunishmentDialogContentViewModel? ViewModel => DataContext as EditRewardPunishmentDialogContentViewModel;

    public EditRewardPunishmentDialogContent()
    {
        InitializeComponent();
        Loaded += OnLoaded;
    }

    private void OnLoaded(object? sender, RoutedEventArgs e)
    {
        // 将焦点设置到类型选择的 ComboBox，而不是员工输入框
        TypeComboBox.Focus();
    }

    private void EmployeeAutoCompleteBox_OnGotFocus(object? sender, RoutedEventArgs e)
    {
        // 只有当用户真正聚焦到控件时，才允许搜索
        if (ViewModel != null)
        {
            ViewModel.AllowSearch = true;
        }
    }
} 