using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Threading;
using HRPayrollSystem.Models;
using MySqlConnector;
using static HRPayrollSystem.Services.ConnectionInfo;

namespace HRPayrollSystem.Views.Dialogs;

public partial class AddUserDialogContent : UserControl
{
    public AddUserDialogContent()
    {
        InitializeComponent();
    }
    

    private void InputField_OnAttachedToVisualTree(object sender, VisualTreeAttachmentEventArgs e)
    {
        // We will set the focus into our input field just after it got attached to the visual tree.
        if (sender is InputElement inputElement)
        {
            Dispatcher.UIThread.InvokeAsync(() =>
            {
                inputElement.Focus(NavigationMethod.Unspecified, KeyModifiers.None);
            });
        }
    }

    private void EmployeeAutoCompleteBox_OnLostFocus(object? sender, RoutedEventArgs e)
    {
        var box = (AutoCompleteBox)sender!;
        if (box.SelectedItem == null)       
            box.Text = string.Empty;     
    }
}