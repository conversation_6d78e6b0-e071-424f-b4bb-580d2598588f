<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             Padding="0, 10"
             x:DataType="vm:AddUserDialogContentViewModel"
             x:Class="HRPayrollSystem.Views.Dialogs.AddUserDialogContent">
    <StackPanel Spacing="10" MinWidth="400">
        
        <TextBlock>员工*</TextBlock>
        <AutoCompleteBox x:Name="EmployeeAutoCompleteBox"
                         Watermark="输入员工姓名或工号进行检索"
                         FilterMode="None"
                         MinimumPrefixLength="1"      
                         MaxDropDownHeight="200" 
                         IsDropDownOpen="{Binding IsDropDownOpen, Mode=TwoWay}"
                         ItemsSource="{Binding SearchResults}"
                         SelectedItem="{Binding EmployeeNumberName}"
                         LostFocus="EmployeeAutoCompleteBox_OnLostFocus"
                         Text="{Binding SearchText, Mode=TwoWay}"
        />
        <TextBlock>初始密码*</TextBlock>
        <TextBox Watermark="请设置初始密码"
                 Text="{CompiledBinding Password}"/>
        
        <TextBlock>用户角色*</TextBlock>
        <ComboBox ItemsSource="{Binding RoleOptions}"
                  SelectedItem="{Binding Role}"
                  PlaceholderText="请选择用户角色"
                  MinWidth="200"/>
    </StackPanel>
</UserControl>