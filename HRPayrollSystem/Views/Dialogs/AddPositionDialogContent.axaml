<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             Padding="0, 10"
             x:DataType="vm:AddPositionDialogContentViewModel"
             x:Class="HRPayrollSystem.Views.Dialogs.AddPositionDialogContent">
    <StackPanel Spacing="10" MinWidth="400">
        <TextBlock>岗位名称*</TextBlock>
        <TextBox Watermark="请输入岗位名称"
                 Text="{CompiledBinding Name}"/>
        
        <TextBlock>岗位描述</TextBlock>
        <TextBox Watermark="请输入岗位描述（可选）"
                 Text="{CompiledBinding Description}"/>
    </StackPanel>
</UserControl> 