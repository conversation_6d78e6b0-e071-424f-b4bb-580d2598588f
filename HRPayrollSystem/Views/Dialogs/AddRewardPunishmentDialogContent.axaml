<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             xmlns:controls="clr-namespace:FluentAvalonia.UI.Controls;assembly=FluentAvalonia"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="500"
             x:Class="HRPayrollSystem.Views.Dialogs.AddRewardPunishmentDialogContent"
             x:DataType="vm:AddRewardPunishmentDialogContentViewModel"
             x:CompileBindings="False">

    <StackPanel Spacing="16" Margin="16">
        <!-- 员工输入 -->
        <StackPanel Spacing="8">
            <TextBlock Text="员工*" FontWeight="SemiBold" />
            <AutoCompleteBox x:Name="EmployeeAutoCompleteBox"
                             Watermark="输入员工姓名或工号进行检索"
                             FilterMode="None"
                             MinimumPrefixLength="1"      
                             MaxDropDownHeight="200" 
                             IsDropDownOpen="{Binding IsDropDownOpen, Mode=TwoWay}"
                             ItemsSource="{Binding SearchResults}"
                             SelectedItem="{Binding EmployeeInput}"
                             LostFocus="EmployeeAutoCompleteBox_OnLostFocus"
                             Text="{Binding SearchText, Mode=TwoWay}"
                             Width="300" />
        </StackPanel>

        <!-- 类型选择 -->
        <StackPanel Spacing="8">
            <TextBlock Text="类型*" FontWeight="SemiBold" />
            <ComboBox ItemsSource="{Binding TypeOptions}"
                      SelectedItem="{Binding Type}"
                      Width="300" />
        </StackPanel>

        <!-- 金额输入 -->
        <StackPanel Spacing="8">
            <TextBlock Text="金额*" FontWeight="SemiBold" />
            <controls:NumberBox Value="{CompiledBinding Amount }"
                                PlaceholderText="请输入金额"
                     Width="300" />
        </StackPanel>

        <!-- 事由输入 -->
        <StackPanel Spacing="8">
            <TextBlock Text="事由" FontWeight="SemiBold" />
            <TextBox Text="{Binding Reason}"
                     Watermark="请输入事由（可选）"
                     Width="300"
                     Height="80"
                     AcceptsReturn="True"
                     TextWrapping="Wrap" />
        </StackPanel>

        <!-- 记录日期选择 -->
        <StackPanel Spacing="8">
            <TextBlock Text="记录日期：" FontWeight="SemiBold" />
            <CalendarDatePicker SelectedDate="{CompiledBinding RecordDate}" Width="300" />
        </StackPanel>
    </StackPanel>

</UserControl> 