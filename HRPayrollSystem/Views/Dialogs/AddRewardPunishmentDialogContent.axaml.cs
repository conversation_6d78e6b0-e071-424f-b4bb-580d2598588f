using Avalonia.Controls;
using Avalonia.Interactivity;

namespace HRPayrollSystem.Views.Dialogs;

public partial class AddRewardPunishmentDialogContent : UserControl
{
    public AddRewardPunishmentDialogContent()
    {
        InitializeComponent();
    }

    private void EmployeeAutoCompleteBox_OnLostFocus(object? sender, RoutedEventArgs e)
    {
        var box = (AutoCompleteBox)sender!;
        if (box.SelectedItem == null)       
            box.Text = string.Empty;     
    }
} 