using Avalonia.Controls;
using Avalonia.Interactivity;
using HRPayrollSystem.ViewModels.Dialogs;

namespace HRPayrollSystem.Views.Dialogs;

public partial class EditUserDialogContent : UserControl
{
    public EditUserDialogContentViewModel? ViewModel => DataContext as EditUserDialogContentViewModel;

    public EditUserDialogContent()
    {
        InitializeComponent();
    }

    private void EmployeeAutoCompleteBox_OnGotFocus(object? sender, RoutedEventArgs e)
    {
        // 只有当用户真正聚焦到控件时，才允许搜索
        if (ViewModel != null)
        {
            ViewModel.AllowSearch = true;
        }
    }
}