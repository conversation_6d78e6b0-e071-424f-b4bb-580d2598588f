<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="600"
             Padding="0, 10"
             x:Class="HRPayrollSystem.Views.Dialogs.EditPersonalInfoDialogContent"
             x:DataType="vm:EditPersonalInfoDialogContentViewModel">
    <ScrollViewer>
        <StackPanel Spacing="10" MinWidth="350" Margin="10">
            <!-- 基本信息 -->
            <TextBlock Classes="h2">基本信息</TextBlock>
            
            <TextBlock>员工编号 *</TextBlock>
            <TextBox Watermark="员工编号"
                     Text="{CompiledBinding EmployeeNumber}"
                     IsReadOnly="True"
                     Background="LightGray"/>
            
            <TextBlock>姓名 *</TextBlock>
            <TextBox Watermark="请输入姓名"
                     Text="{CompiledBinding Name}"/>
            
            <TextBlock>性别</TextBlock>
            <ComboBox ItemsSource="{CompiledBinding GenderOptions}"
                      SelectedItem="{CompiledBinding Gender}"
                      PlaceholderText="请选择性别"/>
            
            <TextBlock>身份证号</TextBlock>
            <TextBox Watermark="请输入身份证号"
                     Text="{CompiledBinding IdCard}"/>
            
            <TextBlock>出生日期</TextBlock>
            <CalendarDatePicker SelectedDate="{CompiledBinding Birthday}"/>
            
            <TextBlock>联系电话</TextBlock>
            <TextBox Watermark="请输入联系电话"
                     Text="{CompiledBinding Phone}"/>
            
            <TextBlock>电子邮箱</TextBlock>
            <TextBox Watermark="请输入电子邮箱"
                     Text="{CompiledBinding Email}"/>
            
            <TextBlock>住址</TextBlock>
            <TextBox Watermark="请输入住址"
                     Text="{CompiledBinding Address}"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     Height="60"/>
            
            <TextBlock Classes="note" Margin="0,10,0,0">
                注：带 * 号的字段为必填项，员工编号不可修改。
            </TextBlock>
        </StackPanel>
    </ScrollViewer>
    
    <UserControl.Styles>
        <Style Selector="TextBlock.h2">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        <Style Selector="TextBlock.note">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="Gray"/>
        </Style>
    </UserControl.Styles>
</UserControl>
