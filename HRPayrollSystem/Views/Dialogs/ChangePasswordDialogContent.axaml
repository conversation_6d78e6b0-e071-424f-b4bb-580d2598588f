<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="400"
             Padding="0, 10"
             x:Class="HRPayrollSystem.Views.Dialogs.ChangePasswordDialogContent"
             x:DataType="vm:ChangePasswordDialogContentViewModel">
    <ScrollViewer>
        <StackPanel Spacing="10" MinWidth="350" Margin="10">
            <!-- 密码修改 -->
            <TextBlock Classes="h2">密码修改</TextBlock>
            
            <TextBlock>员工编号</TextBlock>
            <TextBox Text="{CompiledBinding EmployeeNumber}"
                     IsReadOnly="True"
                     Background="LightGray"/>
            
            <TextBlock>当前角色</TextBlock>
            <TextBox Text="{CompiledBinding Role}"
                     IsReadOnly="True"
                     Background="LightGray"/>
            
            <TextBlock>新密码 *</TextBlock>
            <TextBox Watermark="请输入新密码"
                     Text="{CompiledBinding NewPassword}"
                     PasswordChar="*"/>
            
            <TextBlock>确认新密码 *</TextBlock>
            <TextBox Watermark="请再次输入新密码确认"
                     Text="{CompiledBinding ConfirmPassword}"
                     PasswordChar="*"/>
            
            <TextBlock Classes="note" Margin="0,10,0,0">
                注：密码长度不能少于6位，员工编号和角色不可修改。
            </TextBlock>
        </StackPanel>
    </ScrollViewer>
    
    <UserControl.Styles>
        <Style Selector="TextBlock.h2">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        <Style Selector="TextBlock.note">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="Gray"/>
        </Style>
    </UserControl.Styles>
</UserControl>
