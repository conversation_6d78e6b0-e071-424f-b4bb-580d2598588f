﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="500" d:DesignHeight="600"
             x:Class="HRPayrollSystem.Views.Dialogs.ShowEmployeeDialogContent"
             x:DataType="vm:ShowEmployeeDialogContentViewModel"
             x:CompileBindings="False">
    <ScrollViewer>
        <StackPanel Spacing="16" Margin="16">
            <!-- 基本信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefault}"
                    BorderBrush="{DynamicResource CardStrokeColorDefault}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <StackPanel Spacing="12">
                    <TextBlock Text="基本信息" FontWeight="Bold" FontSize="16" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto">
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="员工编号：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding EmployeeNumber}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="姓名：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Name}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="性别：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Gender}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="身份证号：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding IdCard}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="出生日期：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding Birthday, StringFormat={}{0:yyyy-MM-dd}}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="5" Grid.Column="0" Text="联系电话：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding Phone}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="6" Grid.Column="0" Text="电子邮箱：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="6" Grid.Column="1" Text="{Binding Email}" Margin="0,0,0,8" />
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 地址信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefault}"
                    BorderBrush="{DynamicResource CardStrokeColorDefault}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <StackPanel Spacing="12">
                    <TextBlock Text="地址信息" FontWeight="Bold" FontSize="16" />
                    
                    <TextBlock Text="{Binding Address}" TextWrapping="Wrap" />
                </StackPanel>
            </Border>
            
            <!-- 工作信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefault}"
                    BorderBrush="{DynamicResource CardStrokeColorDefault}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <StackPanel Spacing="12">
                    <TextBlock Text="工作信息" FontWeight="Bold" FontSize="16" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto">
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="部门：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Department}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="职位：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Position}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="入职日期：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding HireDate, StringFormat={}{0:yyyy-MM-dd}}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="试用期结束：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding ProbationEndDate, StringFormat={}{0:yyyy-MM-dd}}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="基本工资：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding BaseSalary, StringFormat={}{0:C2}}" Margin="0,0,0,8" />
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- 离职信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefault}"
                    BorderBrush="{DynamicResource CardStrokeColorDefault}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <StackPanel Spacing="12">
                    <TextBlock Text="离职信息" FontWeight="Bold" FontSize="16" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto">
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="离职日期：" FontWeight="SemiBold" Margin="0,0,10,8" />
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ResignationDate, StringFormat={}{0:yyyy-MM-dd}}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="离职原因：" FontWeight="SemiBold" VerticalAlignment="Top" Margin="0,0,10,0" />
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ResignationReason}" TextWrapping="Wrap" />
                    </Grid>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
