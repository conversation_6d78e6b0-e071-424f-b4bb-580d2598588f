using Avalonia.Controls;
using Avalonia.Interactivity;

namespace HRPayrollSystem.Views.Dialogs;

public partial class AddAttendanceDialogContent : UserControl
{
    public AddAttendanceDialogContent()
    {
        InitializeComponent();
    }

    private void AttendanceAutoCompleteBox_OnLostFocus(object? sender, RoutedEventArgs e)
    {
        var box = (AutoCompleteBox)sender!;
        if (box.SelectedItem == null)
            box.Text = string.Empty;
    }
}
