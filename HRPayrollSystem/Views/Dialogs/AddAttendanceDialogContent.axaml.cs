using System;
using System.Linq;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using HRPayrollSystem.ViewModels.Dialogs;

namespace HRPayrollSystem.Views.Dialogs
{
    public partial class AddAttendanceDialogContent : UserControl
    {
        public AddAttendanceDialogContent()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }
        
        protected override void OnLoaded(RoutedEventArgs e)
        {
            base.OnLoaded(e);
            System.Console.WriteLine("【调试】AddAttendanceDialogContent 已加载");
            
            // 获取 AutoCompleteBox 控件
            var autoCompleteBox = this.FindControl<AutoCompleteBox>("AttendanceAutoCompleteBox");
            if (autoCompleteBox != null)
            {
                System.Console.WriteLine("【调试】找到 AutoCompleteBox 控件");
                
                // 手动设置一些属性
                autoCompleteBox.FilterMode = AutoCompleteFilterMode.None; // 确保禁用内置过滤
                
                
                
                // 订阅 DropDownOpened 事件
                autoCompleteBox.DropDownOpened += (s, args) =>
                {
                    System.Console.WriteLine("【调试】AutoCompleteBox 下拉框已打开");
                };
                
                // 订阅 DropDownClosed 事件
                autoCompleteBox.DropDownClosed += (s, args) =>
                {
                    System.Console.WriteLine("【调试】AutoCompleteBox 下拉框已关闭");
                };
                
                // 订阅 Loaded 事件
                autoCompleteBox.Loaded += (s, args) =>
                {
                    System.Console.WriteLine("【调试】AutoCompleteBox 已完全加载");
                };
                
                // 为了测试，设置一个延迟后手动触发搜索
                Task.Delay(3000).ContinueWith(_ =>
                {
                    Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        if (DataContext is AddAttendanceDialogContentViewModel viewModel)
                        {
                            System.Console.WriteLine("【调试】3秒后手动触发测试搜索");
                            
                            // 设置文本框
                            autoCompleteBox.Text = "张";
                            
                            // 手动触发搜索
                            viewModel.HandleSearchTextChanged("张");
                            
                            // 延迟后尝试打开下拉框
                            Task.Delay(1000).ContinueWith(__ =>
                            {
                                Dispatcher.UIThread.InvokeAsync(() =>
                                {
                                    System.Console.WriteLine("【调试】4秒后检查并尝试打开下拉框");
                                    if (viewModel.SearchResults != null && viewModel.SearchResults.Any())
                                    {
                                        System.Console.WriteLine($"【调试】有搜索结果: {viewModel.SearchResults.Count()} 项，尝试打开下拉框");
                                        
                                        try
                                        {
                                            autoCompleteBox.IsDropDownOpen = true;
                                            viewModel.IsDropDownOpen = true;
                                            System.Console.WriteLine($"【调试】手动设置下拉框状态: {autoCompleteBox.IsDropDownOpen}");
                                        }
                                        catch (Exception ex)
                                        {
                                            System.Console.WriteLine($"【调试】手动设置下拉框状态异常: {ex.Message}");
                                        }
                                    }
                                });
                            });
                        }
                    });
                });
            }
            else
            {
                System.Console.WriteLine("【调试】未找到 AutoCompleteBox 控件");
            }
        }

        private void AttendanceAutoCompleteBox_OnLostFocus(object? sender, RoutedEventArgs e)
        {
            var box = (AutoCompleteBox)sender!;
            if (box.SelectedItem == null)
                box.Text = string.Empty;
        }

        private void AttendanceAutoCompleteBox_OnTextChanged(object? sender, TextChangedEventArgs e)
        {
            if (sender is AutoCompleteBox autoCompleteBox && DataContext is AddAttendanceDialogContentViewModel viewModel)
            {
                string currentText = autoCompleteBox.Text ?? string.Empty;
                System.Console.WriteLine($"【调试】AutoCompleteBox 文本变化: '{currentText}'");
                
                // 直接调用 ViewModel 中的手动处理方法
                viewModel.HandleSearchTextChanged(currentText);
                
                // 强制更新绑定
                System.Console.WriteLine($"【调试】更新文本绑定: '{currentText}'");
                viewModel.SearchText = currentText;
                
                // 为了调试，设置一个延迟，再次尝试打开下拉列表
                if (!string.IsNullOrEmpty(currentText))
                {
                    System.Console.WriteLine("【调试】设置定时器，延迟1秒后再次检查下拉列表");
                    Task.Delay(1000).ContinueWith(_ =>
                    {
                        Dispatcher.UIThread.InvokeAsync(() =>
                        {
                            System.Console.WriteLine($"【调试】1秒后，下拉列表状态: {autoCompleteBox.IsDropDownOpen}");
                            System.Console.WriteLine($"【调试】1秒后，ViewModel中IsDropDownOpen状态: {viewModel.IsDropDownOpen}");
                            
                            // 如果有搜索结果但下拉列表没有打开，则尝试再次打开
                            if (viewModel.SearchResults != null && viewModel.SearchResults.Any() && !autoCompleteBox.IsDropDownOpen)
                            {
                                System.Console.WriteLine($"【调试】1秒后尝试再次打开下拉列表");
                                
                                try
                                {
                                    // 先尝试通过 ViewModel 设置
                                    viewModel.IsDropDownOpen = true;
                                    System.Console.WriteLine($"【调试】通过 ViewModel 设置后: {viewModel.IsDropDownOpen}");
                                    
                                    // 再直接设置控件属性
                                    autoCompleteBox.IsDropDownOpen = true;
                                    System.Console.WriteLine($"【调试】直接设置控件后: {autoCompleteBox.IsDropDownOpen}");
                                    
                                    // 检查设置的结果
                                    System.Console.WriteLine($"【调试】1秒后，设置完成，下拉列表状态: {autoCompleteBox.IsDropDownOpen}");
                                }
                                catch (Exception ex)
                                {
                                    System.Console.WriteLine($"【调试】设置下拉列表状态异常: {ex.Message}");
                                }
                            }
                        });
                    });
                }
            }
        }
    }
}
