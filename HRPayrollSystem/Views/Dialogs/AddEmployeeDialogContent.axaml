﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="800"
             Padding="0, 10"
             x:DataType="vm:AddEmployeeDialogContentViewModel"
             x:Class="HRPayrollSystem.Views.Dialogs.AddEmployeeDialogContent">
    <ScrollViewer>
        <StackPanel Spacing="10" MinWidth="350" Margin="10">
            <!-- 基本信息 -->
            <TextBlock Classes="h2">基本信息</TextBlock>
            
            <TextBlock>员工编号 *</TextBlock>
            <TextBox Watermark="员工编号（必填）"
                     Text="{CompiledBinding EmployeeNumber}"/>
            
            <TextBlock>姓名 *</TextBlock>
            <TextBox Watermark="请输入姓名"
                     Text="{CompiledBinding Name}"/>
            <!-- 工作信息 -->
            <TextBlock Classes="h2">工作信息</TextBlock>
            
            <TextBlock>部门 *</TextBlock>
            <ComboBox ItemsSource="{CompiledBinding DepartmentOptions}"
                      SelectedItem="{CompiledBinding DepartmentNameWithId}"
                      PlaceholderText="请选择部门">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
            
            <TextBlock>职位 *</TextBlock>
            <ComboBox ItemsSource="{CompiledBinding PositionOptions}"
                      SelectedItem="{CompiledBinding PositionNameWithId}"
                      PlaceholderText="请选择职位">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
            
            <TextBlock>入职日期 *</TextBlock>
            <CalendarDatePicker SelectedDate="{CompiledBinding HireDate}" 
                               Watermark="请选择入职日期（必填）"/>
            
            <TextBlock>试用期结束日期 *</TextBlock>
            <CalendarDatePicker SelectedDate="{CompiledBinding ProbationEndDate}"
                               Watermark="请选择试用期结束日期（必填）"/>
            
            <TextBlock>状态 *</TextBlock>
            <ComboBox ItemsSource="{CompiledBinding StatusOptions}"
                      SelectedItem="{CompiledBinding Status}"
                      PlaceholderText="请选择状态"/>
            
            <TextBlock>基本工资</TextBlock>
            <NumericUpDown Value="{CompiledBinding BaseSalary}"
                           Minimum="0"
                           Increment="100"
                           FormatString="C2"/>
            
            <!-- 离职信息 -->
            <TextBlock Classes="h2" IsVisible="{CompiledBinding IsResignationInfoVisible}">离职信息</TextBlock>
            
            <TextBlock IsVisible="{CompiledBinding IsResignationInfoVisible}">离职日期 *</TextBlock>
            <CalendarDatePicker SelectedDate="{CompiledBinding ResignationDate}"
                               IsVisible="{CompiledBinding IsResignationInfoVisible}"
                               Watermark="选择离职状态时必填"/>
            
            <TextBlock IsVisible="{CompiledBinding IsResignationInfoVisible}">离职原因</TextBlock>
            <TextBox Watermark="请输入离职原因"
                     Text="{CompiledBinding ResignationReason}"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     Height="60"
                     IsVisible="{CompiledBinding IsResignationInfoVisible}"/>
            
            <TextBlock Classes="h2">其他信息</TextBlock>

            <TextBlock>性别</TextBlock>
            <ComboBox ItemsSource="{CompiledBinding GenderOptions}"
                      SelectedItem="{CompiledBinding Gender}"
                      PlaceholderText="请选择性别"/>
            
            <TextBlock>身份证号</TextBlock>
            <TextBox Watermark="请输入身份证号"
                     Text="{CompiledBinding IdCard}"/>
            
            <TextBlock>出生日期</TextBlock>
            <CalendarDatePicker SelectedDate="{CompiledBinding Birthday}"/>
            
            <TextBlock>联系电话</TextBlock>
            <TextBox Watermark="请输入联系电话"
                     Text="{CompiledBinding Phone}"/>
            
            <TextBlock>电子邮箱</TextBlock>
            <TextBox Watermark="请输入电子邮箱"
                     Text="{CompiledBinding Email}"/>
            
            <TextBlock>住址</TextBlock>
            <TextBox Watermark="请输入住址"
                     Text="{CompiledBinding Address}"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     Height="60"/>
            
            
            
            
            <TextBlock Classes="note" Margin="0,10,0,0">注：带 * 号的字段为必填项</TextBlock>
        </StackPanel>
    </ScrollViewer>
    
    <UserControl.Styles>
        <Style Selector="TextBlock.h2">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        <Style Selector="TextBlock.note">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="Gray"/>
        </Style>
    </UserControl.Styles>
</UserControl>