<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             Padding="0, 10"
             x:DataType="vm:AddDepartmentDialogContentViewModel"
             x:Class="HRPayrollSystem.Views.Dialogs.AddDepartmentDialogContent">
    <StackPanel Spacing="10" MinWidth="400">
        <TextBlock>部门名称*</TextBlock>
        <TextBox Watermark="请输入部门名称"
                 Text="{CompiledBinding Name}"/>
        
        <TextBlock>上级部门*</TextBlock>
        <ComboBox Name="ParentDepartmentComboBox"
                  PlaceholderText="请选择上级部门"
                  ItemsSource="{CompiledBinding DepartmentOptions}"
                  SelectedItem="{CompiledBinding ParentNameWithId, Mode=TwoWay}">
            <ComboBox.ItemTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding}"/>
                </DataTemplate>
            </ComboBox.ItemTemplate>
        </ComboBox>
    </StackPanel>
</UserControl>