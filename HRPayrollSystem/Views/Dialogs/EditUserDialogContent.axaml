<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="500" d:DesignHeight="400"
             x:Class="HRPayrollSystem.Views.Dialogs.EditUserDialogContent"
             x:DataType="vm:EditUserDialogContentViewModel">

    <ScrollViewer>
        <StackPanel Spacing="16" Margin="24">
            <!-- 员工编号 -->
            <StackPanel Spacing="8">
                <TextBlock Text="员工*" FontWeight="SemiBold" />
                <AutoCompleteBox x:Name="EmployeeAutoCompleteBox"
                                 Watermark="输入员工姓名或工号进行检索"
                                 FilterMode="None"
                                 MinimumPrefixLength="1"      
                                 MaxDropDownHeight="200" 
                                 IsDropDownOpen="{Binding IsDropDownOpen, Mode=TwoWay}"
                                 ItemsSource="{Binding SearchResults}"
                                 SelectedItem="{Binding EmployeeInput}"
                                 Text="{Binding SearchText, Mode=TwoWay}"
                                 MinWidth="350"
                                 GotFocus="EmployeeAutoCompleteBox_OnGotFocus" />
            </StackPanel>

            <!-- 角色选择 -->
            <StackPanel Spacing="8">
                <TextBlock Text="角色*" FontWeight="SemiBold" />
                <ComboBox ItemsSource="{Binding RoleOptions}"
                          SelectedItem="{Binding Role}"
                          PlaceholderText="请选择角色"
                          MinWidth="350" />
            </StackPanel>

            <!-- 密码 -->
            <StackPanel Spacing="8">
                <TextBlock Text="密码" FontWeight="SemiBold" />
                <TextBox Text="{Binding Password}"
                         PasswordChar="*"
                         Watermark="留空表示保持原密码不变"
                         MinWidth="350" />
                <TextBlock Text="提示：留空表示保持原密码不变"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
            </StackPanel>

        </StackPanel>
    </ScrollViewer>
</UserControl>