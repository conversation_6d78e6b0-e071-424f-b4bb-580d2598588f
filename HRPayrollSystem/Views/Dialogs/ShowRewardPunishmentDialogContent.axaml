<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="500" d:DesignHeight="600"
             x:Class="HRPayrollSystem.Views.Dialogs.ShowRewardPunishmentDialogContent"
             x:DataType="vm:ShowRewardPunishmentDialogContentViewModel"
             x:CompileBindings="False">

    <ScrollViewer>
        <StackPanel Spacing="16" Margin="16">
            <!-- 员工信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefault}"
                    BorderBrush="{DynamicResource CardStrokeColorDefault}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <StackPanel Spacing="12">
                    <TextBlock Text="员工信息" FontWeight="Bold" FontSize="16" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto">
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="员工姓名：" FontWeight="SemiBold" Margin="0,0,0,8" />
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding EmployeeName}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="员工编号：" FontWeight="SemiBold" />
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding EmployeeNumber}" />
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 奖罚信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefault}"
                    BorderBrush="{DynamicResource CardStrokeColorDefault}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <StackPanel Spacing="12">
                    <TextBlock Text="奖罚信息" FontWeight="Bold" FontSize="16" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto">
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="类型：" FontWeight="SemiBold" Margin="0,0,0,8" />
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Type}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="金额：" FontWeight="SemiBold" Margin="0,0,0,8" />
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Amount, StringFormat={}{0:C}}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="记录日期：" FontWeight="SemiBold" Margin="0,0,0,8" />
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding RecordDate, StringFormat={}{0:yyyy-MM-dd}}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="事由：" FontWeight="SemiBold" VerticalAlignment="Top" />
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Reason}" 
                                   TextWrapping="Wrap" />
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 时间信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefault}"
                    BorderBrush="{DynamicResource CardStrokeColorDefault}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <StackPanel Spacing="12">
                    <TextBlock Text="时间信息" FontWeight="Bold" FontSize="16" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto">
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="创建时间：" FontWeight="SemiBold" Margin="0,0,0,8" />
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CreatedAt, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" Margin="0,0,0,8" />
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="更新时间：" FontWeight="SemiBold" />
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding UpdatedAt, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" />
                    </Grid>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>

</UserControl>