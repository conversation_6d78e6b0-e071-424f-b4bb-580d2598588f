<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="450"
             x:Class="HRPayrollSystem.Views.Dialogs.TransferEmployeeDialogContent"
             x:DataType="vm:TransferEmployeeDialogContentViewModel">
  <StackPanel Spacing="15" Margin="15">
    
    <!-- 部门调动信息 -->
    <StackPanel Spacing="10">
      <TextBlock Text="当前部门:" FontWeight="Bold"/>
      <TextBlock Text="{Binding CurrentDepartmentName}" Margin="5,0,0,10"/>
      
      <TextBlock Text="目标部门:" FontWeight="Bold"/>
      <ComboBox HorizontalAlignment="Stretch"
                ItemsSource="{Binding Departments}" 
                SelectedItem="{Binding SelectedDepartment}">
        <ComboBox.ItemTemplate>
          <DataTemplate>
            <TextBlock Text="{Binding Name}"/>
          </DataTemplate>
        </ComboBox.ItemTemplate>
      </ComboBox>
    </StackPanel>

    <!-- 职位调动信息 -->
    <StackPanel Spacing="10" Margin="0,10,0,0">
      <TextBlock Text="当前职位:" FontWeight="Bold"/>
      <TextBlock Text="{Binding CurrentPositionName}" Margin="5,0,0,10"/>
      
      <TextBlock Text="目标职位:" FontWeight="Bold"/>
      <ComboBox HorizontalAlignment="Stretch"
                ItemsSource="{Binding Positions}" 
                SelectedItem="{Binding SelectedPosition}">
        <ComboBox.ItemTemplate>
          <DataTemplate>
            <TextBlock Text="{Binding Name}"/>
          </DataTemplate>
        </ComboBox.ItemTemplate>
      </ComboBox>
    </StackPanel>
    
    <!-- 调动日期 -->
    <TextBlock Text="调动日期:" FontWeight="Bold" Margin="0,10,0,0"/>
    <CalendarDatePicker HorizontalAlignment="Stretch" SelectedDate="{Binding TransferDate}"/>
    
    <!-- 调动原因 -->
    <TextBlock Text="调动原因:" FontWeight="Bold" Margin="0,10,0,0"/>
    <TextBox Text="{Binding Reason}" Height="80" AcceptsReturn="True"/>
    
  </StackPanel>
</UserControl> 