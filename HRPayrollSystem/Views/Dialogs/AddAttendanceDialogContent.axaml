<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="400"
             x:Class="HRPayrollSystem.Views.Dialogs.AddAttendanceDialogContent"
             x:DataType="vm:AddAttendanceDialogContentViewModel"
             x:CompileBindings="False">

    <StackPanel Spacing="16" Margin="16">
        <!-- 员工输入 -->
        <StackPanel Spacing="8">
            <TextBlock Text="员工*" FontWeight="SemiBold" />
            <AutoCompleteBox x:Name="AttendanceAutoCompleteBox"
                             Watermark="输入员工姓名或工号进行检索"
                             FilterMode="None"
                             MinimumPrefixLength="1"
                             MaxDropDownHeight="200"
                             IsDropDownOpen="{Binding IsDropDownOpen, Mode=TwoWay}"
                             ItemsSource="{Binding SearchResults}"
                             SelectedItem="{Binding EmployeeInput}"
                             Text="{Binding SearchText, Mode=TwoWay}"
                             Width="300"
                             LostFocus="AttendanceAutoCompleteBox_OnLostFocus" />
        </StackPanel>

        <!-- 员工信息显示 -->
        <StackPanel Spacing="8">
            <TextBlock Text="员工信息" FontWeight="SemiBold" />
            <StackPanel Orientation="Horizontal" Spacing="16">
                <TextBlock Text="{Binding SelectedEmployee.Department, StringFormat='部门: {0}'}" />
                <TextBlock Text="{Binding SelectedEmployee.Position, StringFormat='岗位: {0}'}" />
            </StackPanel>
        </StackPanel>

        <!-- 考勤日期 -->
        <StackPanel Spacing="8">
            <TextBlock Text="考勤日期*" FontWeight="SemiBold" />
            <CalendarDatePicker SelectedDate="{Binding AttendanceDate}" Width="300" />
        </StackPanel>

        <!-- 考勤状态 -->
        <StackPanel Spacing="8">
            <TextBlock Text="考勤状态*" FontWeight="SemiBold" />
            <ComboBox ItemsSource="{Binding StatusOptions}"
                      SelectedItem="{Binding Status}"
                      Width="300" />
        </StackPanel>

        <!-- 备注 -->
        <StackPanel Spacing="8">
            <TextBlock Text="备注" FontWeight="SemiBold" />
            <TextBox Text="{Binding Notes}"
                     Watermark="请输入备注（可选）"
                     Width="300"
                     Height="80"
                     AcceptsReturn="True"
                     TextWrapping="Wrap" />
        </StackPanel>
    </StackPanel>
</UserControl>
