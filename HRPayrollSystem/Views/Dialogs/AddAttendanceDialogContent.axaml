<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:HRPayrollSystem.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="400"
             x:Class="HRPayrollSystem.Views.Dialogs.AddAttendanceDialogContent"
             x:DataType="vm:AddAttendanceDialogContentViewModel">

    <Grid RowDefinitions="Auto,Auto,Auto,Auto,Auto" ColumnDefinitions="Auto,*" Margin="10">
        <TextBlock Grid.Row="0" Grid.Column="0" Text="选择员工:" VerticalAlignment="Center" Margin="0,0,10,0" />
        <AutoCompleteBox
            x:Name="AttendanceAutoCompleteBox"
            Grid.Row="0" Grid.Column="1" HorizontalAlignment="Stretch" Margin="0,5"
            Text="{Binding SearchText, Mode=TwoWay}"
            ItemsSource="{Binding SearchResults}"
            SelectedItem="{Binding EmployeeInput}"
            FilterMode="None"
            MinimumPrefixLength="1"
            MaxDropDownHeight="200"
            IsDropDownOpen="{Binding IsDropDownOpen, Mode=TwoWay}"
            TextChanged="AttendanceAutoCompleteBox_OnTextChanged"
            LostFocus="AttendanceAutoCompleteBox_OnLostFocus"
            Watermark="请输入员工工号或姓名进行搜索" />

        <TextBlock Grid.Row="1" Grid.Column="0" Text="部门:" VerticalAlignment="Center" Margin="0,0,10,0" />
        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedEmployee.Department}" VerticalAlignment="Center"
                   Margin="0,5" />

        <TextBlock Grid.Row="2" Grid.Column="0" Text="岗位:" VerticalAlignment="Center" Margin="0,0,10,0" />
        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedEmployee.Position}" VerticalAlignment="Center"
                   Margin="0,5" />

        <TextBlock Grid.Row="3" Grid.Column="0" Text="日期:" VerticalAlignment="Center" Margin="0,0,10,0" />
        <CalendarDatePicker Grid.Row="3" Grid.Column="1" HorizontalAlignment="Stretch" Margin="0,5"
                            SelectedDate="{Binding AttendanceDate}" />

        <TextBlock Grid.Row="4" Grid.Column="0" Text="考勤状态:" VerticalAlignment="Top" Margin="0,10,10,0" />
        <StackPanel Grid.Row="4" Grid.Column="1" Orientation="Vertical" Margin="0,5">
            <CheckBox Content="正常" IsChecked="{Binding IsNormal}" />
            <CheckBox Content="迟到" IsChecked="{Binding IsLate}" />
            <CheckBox Content="早退" IsChecked="{Binding IsEarlyLeave}" />
            <CheckBox Content="旷工" IsChecked="{Binding IsAbsent}" />
            <CheckBox Content="请假" IsChecked="{Binding IsLeave}" />

            <TextBlock Text="备注:" Margin="0,10,0,5" />
            <TextBox Text="{Binding Notes}" Height="60" AcceptsReturn="True" />
        </StackPanel>

    </Grid>
</UserControl>