<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:HRPayrollSystem.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:sty="using:FluentAvalonia.Styling"
        xmlns:ui="using:FluentAvalonia.UI.Controls"
        xmlns:ic="using:FluentIcons.Avalonia"
        xmlns:uip="using:FluentAvalonia.UI.Controls.Primitives"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="HRPayrollSystem.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Icon="/Assets/avalonia-logo.ico"
        Title="HRPayrollSystem">

    <Design.DataContext>
        <!-- This only sets the DataContext for the previewer in an IDE,
             to set the actual DataContext for runtime, set the DataContext property in code (look at App.axaml.cs) -->
        <vm:MainWindowViewModel />
    </Design.DataContext>
    
    <ui:NavigationView x:Name="NavView"
                       OpenPaneLength="135"
                       ItemInvoked="OnNavItemInvoked"
                       SettingsItem=""
                       IsSettingsVisible="False">
        <ui:NavigationView.MenuItems>
            <!-- 动态菜单项将在代码中添加 -->
        </ui:NavigationView.MenuItems>
        <ui:NavigationView.FooterMenuItems>
            <ui:NavigationViewItem Content="个人设置"
                                   IconSource="Settings"
                                   Tag="HRPayrollSystem.Views.Pages.PersonalSettingPage" >
                
            </ui:NavigationViewItem >

                
        </ui:NavigationView.FooterMenuItems>
        <ui:Frame x:Name="MainFrame" />
    </ui:NavigationView>

</Window>