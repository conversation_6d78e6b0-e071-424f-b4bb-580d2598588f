using System;
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.ReactiveUI;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.ViewModels;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.Views;

public partial class LoginWindow : ReactiveWindow<LoginWindowViewModel>
{
    public LoginWindow()
    {
        InitializeComponent();
        
        this.WhenActivated(disposables =>
        {
            // 监听登录结果
            ViewModel?.LoginResult
                .ObserveOn(RxApp.MainThreadScheduler)
                .Subscribe(user =>
                {
                    if (user != null)
                    {
                        // 设置当前登录用户
                        CurrentUserService.CurrentUser = user;
                        
                        // 登录成功，打开主窗口
                        var mainWindow = new MainWindow
                        {
                            DataContext = new MainWindowViewModel(),
                        };
                        mainWindow.Show();
                        this.Close();
                    }
                })
                .DisposeWith(disposables);
        });
    }


}