<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:sty="using:FluentAvalonia.Styling"
             x:Class="HRPayrollSystem.App"
             xmlns:local="using:HRPayrollSystem"
             xmlns:ui="using:FluentAvalonia.UI.Controls"
             xmlns:controls="clr-namespace:FluentAvalonia.UI.Controls;assembly=FluentAvalonia"
             RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>
  
    <Application.Styles>
        <sty:FluentAvaloniaTheme />
        <Style Selector="ui|NavigationViewItem">
            <Setter Property="MinHeight" Value="45"/>
            <Setter Property="Padding"   Value="0,6, 0, 6"/>
            <Setter Property="FontSize"  Value="14"/>
            <!-- 如果想放大图标，请重写 IconSource 的样式或把 SymbolIcon 的 FontSize 设大 -->
        </Style>

    </Application.Styles>
    <Application.Resources>
        <local:StringEqualsConverter x:Key="StringEqualsConverter"/>
        <Color x:Key="SystemAccentColor">#46B0B7</Color>
        <Color x:Key="SystemAccentColorLight1">#46B0B7</Color>
        <Color x:Key="SystemAccentColorDark1">#46B0B7</Color>
    </Application.Resources>
</Application>