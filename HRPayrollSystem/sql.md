-- ----------------------------
-- 1. 部门表
-- ----------------------------
DROP TABLE IF EXISTS `t_department`;
CREATE TABLE `t_department` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`name` VARCHAR(50) NOT NULL COMMENT '部门名称',
`parent_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '上级部门ID (0表示顶级部门)',
`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志(0:未删除, 1:已删除)',
PRIMARY KEY (`id`),
INDEX `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- ----------------------------
-- 2. 岗位表
-- ----------------------------
DROP TABLE IF EXISTS `t_position`;
CREATE TABLE `t_position` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`name` VARCHAR(50) NOT NULL COMMENT '岗位名称',
`description` VARCHAR(255) DEFAULT NULL COMMENT '岗位描述',
`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志(0:未删除, 1:已删除)',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位表';

-- ----------------------------
-- 3. 员工档案表
-- ----------------------------
DROP TABLE IF EXISTS `t_employee`;
CREATE TABLE `t_employee` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`employee_number` VARCHAR(50) NOT NULL COMMENT '工号',
`name` VARCHAR(50) NOT NULL COMMENT '姓名',
`gender` VARCHAR(10) DEFAULT NULL COMMENT '性别 (建议值: 男, 女)',
`id_card` VARCHAR(20) DEFAULT NULL COMMENT '身份证号',
`birthday` DATE DEFAULT NULL COMMENT '出生日期',
`phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
`email` VARCHAR(100) DEFAULT NULL COMMENT '电子邮箱',
`address` VARCHAR(255) DEFAULT NULL COMMENT '联系地址',
`department_id` INT UNSIGNED DEFAULT NULL COMMENT '所属部门ID',
`position_id` INT UNSIGNED DEFAULT NULL COMMENT '所属岗位ID',
`hire_date` DATE DEFAULT NULL COMMENT '入职日期',
`probation_end_date` DATE DEFAULT NULL COMMENT '试用期结束日期',
`status` VARCHAR(20) DEFAULT NULL COMMENT '员工状态 (建议值: 在职, 试用期, 离职)',
`base_salary` DECIMAL(10, 2) UNSIGNED DEFAULT 0.00 COMMENT '基本工资',
`resignation_date` DATE DEFAULT NULL COMMENT '离职日期',
`resignation_reason` TEXT COMMENT '离职原因',
`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志(0:未删除, 1:已删除)',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_employee_number` (`employee_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工档案表';

-- ----------------------------
-- 4. 用户表 (系统登录账号)
-- ----------------------------
DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`employee_number` VARCHAR(50) NOT NULL COMMENT '员工工号',
`password_hash` VARCHAR(255) NOT NULL COMMENT '加密后的密码',
`role` VARCHAR(50) DEFAULT NULL COMMENT '角色 (系统管理员, 人事经理, 员工)',
`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志(0:未删除, 1:已删除)',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_employee_number` (`employee_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utt_userf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- 9. 奖罚记录表
-- ----------------------------
DROP TABLE IF EXISTS `t_reward_penalty`;
CREATE TABLE `t_reward_penalty` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`employee_id` INT UNSIGNED NOT NULL COMMENT '员工ID',
`type` VARCHAR(10) NOT NULL COMMENT '类型 (建议值: 奖励, 处罚)',
`amount` DECIMAL(10, 2) NOT NULL COMMENT '金额 (正为奖励, 负为处罚)',
`reason` TEXT COMMENT '事由',
`record_date` DATE NOT NULL COMMENT '记录日期',
`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志(0:未删除, 1:已删除)',
PRIMARY KEY (`id`),
INDEX `idx_employee_date` (`employee_id`, `record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='奖罚记录表';

-- ----------------------------
-- 10. 考勤记录表
-- ----------------------------
DROP TABLE IF EXISTS `t_attendance`;
CREATE TABLE `t_attendance` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`employee_id` INT UNSIGNED NOT NULL COMMENT '员工ID',
`date` DATE NOT NULL COMMENT '考勤日期',
`status` VARCHAR(20) NOT NULL COMMENT '考勤状态 (建议值: 正常, 迟到, 早退, 旷工, 请假)',
`notes` VARCHAR(255) DEFAULT NULL COMMENT '备注',
`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志(0:未删除, 1:已删除)',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_employee_date` (`employee_id`, `date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考勤记录表';

-- ----------------------------
-- 11. 工资单表
-- ----------------------------
DROP TABLE IF EXISTS `t_payroll`;
CREATE TABLE `t_payroll` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`employee_id` INT UNSIGNED NOT NULL COMMENT '员工ID',
`pay_month` VARCHAR(7) NOT NULL COMMENT '工资月份 (格式: YYYY-MM)',
`base_salary` DECIMAL(10, 2) DEFAULT 0.00 COMMENT '基本工资',
`reward_amount` DECIMAL(10, 2) DEFAULT 0.00 COMMENT '当月奖励总额',
`penalty_amount` DECIMAL(10, 2) DEFAULT 0.00 COMMENT '当月处罚总额',
`attendance_deduction` DECIMAL(10, 2) DEFAULT 0.00 COMMENT '考勤扣款',
`tax` DECIMAL(10, 2) DEFAULT 0.00 COMMENT '个人所得税',
`net_salary` DECIMAL(10, 2) DEFAULT 0.00 COMMENT '实发工资',
`status` VARCHAR(20) DEFAULT '待确认' COMMENT '状态 (建议值: 待确认, 已发放)',
`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志(0:未删除, 1:已删除)',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_employee_month` (`employee_id`, `pay_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工资单表';

-- ----------------------------
-- 12. 调动记录表
-- ----------------------------
DROP TABLE IF EXISTS `t_transfer_log`;
CREATE TABLE `t_transfer_log` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`employee_id` INT UNSIGNED NOT NULL COMMENT '员工ID',
`transfer_date` DATE NOT NULL COMMENT '调动日期',
`type` VARCHAR(20) NOT NULL COMMENT '调动类型 (建议值: 部门调动, 岗位调动)',
`old_department_id` INT UNSIGNED DEFAULT NULL COMMENT '原部门ID',
`new_department_id` INT UNSIGNED DEFAULT NULL COMMENT '新部门ID',
`old_position_id` INT UNSIGNED DEFAULT NULL COMMENT '原岗位ID',
`new_position_id` INT UNSIGNED DEFAULT NULL COMMENT '新岗位ID',
`reason` TEXT COMMENT '调动原因',
`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除标志(0:未删除, 1:已删除)',
PRIMARY KEY (`id`),
INDEX `idx_employee_id` (`employee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工调动记录表';