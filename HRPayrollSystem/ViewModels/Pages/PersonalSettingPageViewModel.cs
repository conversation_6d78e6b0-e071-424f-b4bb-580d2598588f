using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.Views;
using HRPayrollSystem.Views.Dialogs;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Pages;

public class PersonalSettingPageViewModel : ViewModelBase
{
    private readonly UserManagementService _userManagementService;
    private readonly EmployeeManagementService _employeeManagementService;
    
    private string _currentUserInfo = "";

    public ReactiveCommand<Unit, Unit> EditPersonalInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ChangePasswordCommand { get; }
    public ReactiveCommand<Unit, Unit> LogoutCommand { get; }

    public PersonalSettingPageViewModel(UserManagementService userManagementService, EmployeeManagementService employeeManagementService)
    {
        _userManagementService = userManagementService;
        _employeeManagementService = employeeManagementService;
        
        EditPersonalInfoCommand = ReactiveCommand.CreateFromTask(ShowEditPersonalInfoDialogAsync);
        ChangePasswordCommand = ReactiveCommand.CreateFromTask(ShowChangePasswordDialogAsync);
        LogoutCommand = ReactiveCommand.CreateFromTask(LogoutAsync);
        
        UpdateCurrentUserInfo();
    }

    private void UpdateCurrentUserInfo()
    {
        var currentUser = CurrentUserService.CurrentUser;
        if (currentUser != null)
        {
            CurrentUserInfo = $"当前用户: {currentUser.EmployeeNumber} | 角色: {currentUser.Role}";
        }
        else
        {
            CurrentUserInfo = "未登录";
        }
    }

    private async Task ShowEditPersonalInfoDialogAsync()
    {
        var currentUser = CurrentUserService.CurrentUser;
        if (currentUser == null)
        {
            var errorDialog = new ContentDialog
            {
                Content = "未获取到当前用户信息，请重新登录",
                Title = "错误",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            await errorDialog.ShowAsync();
            return;
        }

        // 根据员工编号获取员工信息
        var currentEmployee = await _employeeManagementService.FindByEmployeeNumberAsync(currentUser.EmployeeNumber ?? "");
        
        if (currentEmployee == null)
        {
            var errorDialog = new ContentDialog
            {
                Content = "未找到对应的员工信息",
                Title = "错误",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            await errorDialog.ShowAsync();
            return;
        }

        var dialog = new ContentDialog()
        {
            Title = "编辑个人信息",
            PrimaryButtonText = "保存",
            SecondaryButtonText = "重置",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new EditPersonalInfoDialogContentViewModel(dialog, _employeeManagementService, currentEmployee);
        dialog.Content = new EditPersonalInfoDialogContent()
        {
            DataContext = viewModel
        };

        var result = await dialog.ShowAsync();
        // 如果保存成功，更新显示信息
        if (result == ContentDialogResult.Primary)
        {
            UpdateCurrentUserInfo();
        }
    }

    private async Task ShowChangePasswordDialogAsync()
    {
        var currentUser = CurrentUserService.CurrentUser;
        if (currentUser == null)
        {
            var errorDialog = new ContentDialog
            {
                Content = "未获取到当前用户信息，请重新登录",
                Title = "错误",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            await errorDialog.ShowAsync();
            return;
        }

        var dialog = new ContentDialog()
        {
            Title = "修改密码",
            PrimaryButtonText = "保存",
            SecondaryButtonText = "重置",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new ChangePasswordDialogContentViewModel(dialog, _userManagementService, currentUser);
        dialog.Content = new ChangePasswordDialogContent()
        {
            DataContext = viewModel
        };

        var result = await dialog.ShowAsync();
        // 密码修改成功后不需要更新显示信息
    }

    private async Task LogoutAsync()
    {
        var confirmDialog = new ContentDialog
        {
            Title = "确认操作",
            Content = "您确定要退出登录吗？",
            PrimaryButtonText = "确定",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Secondary
        };

        var result = await confirmDialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            // 清除当前用户信息
            CurrentUserService.CurrentUser = null;
            
            // 获取应用程序生命周期
            if (Avalonia.Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                // 关闭所有窗口
                var windows = new List<Window>();
                foreach (var window in desktop.Windows)
                {
                    windows.Add(window);
                }
                
                // 创建登录窗口
                var userManagementService = ServiceProvider.GetService<UserManagementService>();
                var loginWindow = new LoginWindow
                {
                    DataContext = new LoginWindowViewModel(userManagementService)
                };
                
                // 设置新的主窗口
                desktop.MainWindow = loginWindow;
                
                // 显示登录窗口
                loginWindow.Show();
                
                // 关闭所有旧窗口
                foreach (var window in windows)
                {
                    if (window != loginWindow)
                    {
                        window.Close();
                    }
                }
            }
        }
    }

    public string CurrentUserInfo
    {
        get => _currentUserInfo;
        set => this.RaiseAndSetIfChanged(ref _currentUserInfo, value);
    }
}