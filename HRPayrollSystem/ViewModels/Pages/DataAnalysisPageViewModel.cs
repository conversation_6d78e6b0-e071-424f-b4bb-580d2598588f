using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using ReactiveUI;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using Avalonia.Platform.Storage;

namespace HRPayrollSystem.ViewModels.Pages;

public class DataAnalysisPageViewModel : ViewModelBase
{
    private readonly EmployeeManagementService _employeeService;
    private readonly PayrollManagementService _payrollService;
    private readonly AttendanceManagementService _attendanceService;
    private readonly RewardPunishmentManagementService _rewardPunishmentService;

    private ObservableCollection<ISeries> _departmentDistributionChart = new();
    private ObservableCollection<ISeries> _salaryTrendChart = new();
    private ObservableCollection<ISeries> _attendanceStatusChart = new();
    private ObservableCollection<ISeries> _rewardPunishmentChart = new();
    private ObservableCollection<ISeries> _salaryRangeChart = new();
    private ObservableCollection<ISeries> _monthlyPayrollChart = new();

    public ObservableCollection<ISeries> DepartmentDistributionChart
    {
        get => _departmentDistributionChart;
        set => this.RaiseAndSetIfChanged(ref _departmentDistributionChart, value);
    }

    public ObservableCollection<ISeries> SalaryTrendChart
    {
        get => _salaryTrendChart;
        set => this.RaiseAndSetIfChanged(ref _salaryTrendChart, value);
    }

    public ObservableCollection<ISeries> AttendanceStatusChart
    {
        get => _attendanceStatusChart;
        set => this.RaiseAndSetIfChanged(ref _attendanceStatusChart, value);
    }

    public ObservableCollection<ISeries> RewardPunishmentChart
    {
        get => _rewardPunishmentChart;
        set => this.RaiseAndSetIfChanged(ref _rewardPunishmentChart, value);
    }

    public ObservableCollection<ISeries> SalaryRangeChart
    {
        get => _salaryRangeChart;
        set => this.RaiseAndSetIfChanged(ref _salaryRangeChart, value);
    }

    public ObservableCollection<ISeries> MonthlyPayrollChart
    {
        get => _monthlyPayrollChart;
        set => this.RaiseAndSetIfChanged(ref _monthlyPayrollChart, value);
    }

    public ICommand ExportDepartmentDistributionToCSVCommand { get; }
    public ICommand ExportSalaryTrendToCSVCommand { get; }
    public ICommand ExportAttendanceStatusToCSVCommand { get; }
    public ICommand ExportRewardPunishmentToCSVCommand { get; }
    public ICommand ExportSalaryRangeToCSVCommand { get; }
    public ICommand ExportMonthlyPayrollToCSVCommand { get; }

    public DataAnalysisPageViewModel()
    {
        _employeeService = ServiceProvider.GetService<EmployeeManagementService>();
        _payrollService = ServiceProvider.GetService<PayrollManagementService>();
        _attendanceService = ServiceProvider.GetService<AttendanceManagementService>();
        _rewardPunishmentService = ServiceProvider.GetService<RewardPunishmentManagementService>();

        // 初始化命令
        ExportDepartmentDistributionToCSVCommand = ReactiveCommand.CreateFromTask(ExportDepartmentDistributionToCSV);
        ExportSalaryTrendToCSVCommand = ReactiveCommand.CreateFromTask(ExportSalaryTrendToCSV);
        ExportAttendanceStatusToCSVCommand = ReactiveCommand.CreateFromTask(ExportAttendanceStatusToCSV);
        ExportRewardPunishmentToCSVCommand = ReactiveCommand.CreateFromTask(ExportRewardPunishmentToCSV);
        ExportSalaryRangeToCSVCommand = ReactiveCommand.CreateFromTask(ExportSalaryRangeToCSV);
        ExportMonthlyPayrollToCSVCommand = ReactiveCommand.CreateFromTask(ExportMonthlyPayrollToCSV);

        LoadChartsData();
    }

    private async void LoadChartsData()
    {
        await LoadDepartmentDistributionChart();
        await LoadSalaryTrendChart();
        await LoadAttendanceStatusChart();
        await LoadRewardPunishmentChart();
        await LoadSalaryRangeChart();
        await LoadMonthlyPayrollChart();
    }

    private async Task LoadDepartmentDistributionChart()
    {
        var employees = await _employeeService.FindAllAsync();
        var departmentGroups = employees.GroupBy(e => e.Department ?? "未分配")
            .Select(g => new { Department = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .ToList();

        DepartmentDistributionChart = new ObservableCollection<ISeries>(
            departmentGroups.Select(item => new PieSeries<double>
            {
                Name = item.Department,
                Values = new double[] { item.Count },
                DataLabelsPaint = new SolidColorPaint(SKColors.White)
            })
        );
    }

    private async Task LoadSalaryTrendChart()
    {
        var employees = await _employeeService.FindAllAsync();
        var salaryByDepartment = employees.GroupBy(e => e.Department ?? "未分配")
            .Select(g => new { Department = g.Key, AvgSalary = g.Average(e => (double)e.BaseSalary) })
            .OrderBy(x => x.Department)
            .ToList();

        SalaryTrendChart = new ObservableCollection<ISeries>
        {
            new ColumnSeries<double>
            {
                Name = "平均薪资",
                Values = salaryByDepartment.Select(x => x.AvgSalary).ToArray()
            }
        };
    }

    private async Task LoadAttendanceStatusChart()
    {
        var attendances = await _attendanceService.FindAllAsync();
        var statusGroups = attendances.GroupBy(a => a.Status ?? "未知")
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToList();

        AttendanceStatusChart = new ObservableCollection<ISeries>(
            statusGroups.Select(item => new PieSeries<double>
            {
                Name = item.Status,
                Values = new double[] { item.Count },
                DataLabelsPaint = new SolidColorPaint(SKColors.White)
            })
        );
    }

    private async Task LoadRewardPunishmentChart()
    {
        var records = await _rewardPunishmentService.FindAllAsync();
        var typeGroups = records.GroupBy(r => r.Type)
            .Select(g => new { Type = g.Key, Count = g.Count(), TotalAmount = g.Sum(r => (double)r.Amount) })
            .ToList();

        RewardPunishmentChart = new ObservableCollection<ISeries>
        {
            new ColumnSeries<double>
            {
                Name = "数量",
                Values = typeGroups.Select(x => (double)x.Count).ToArray()
            },
            new LineSeries<double>
            {
                Name = "总金额",
                Values = typeGroups.Select(x => x.TotalAmount).ToArray(),
                GeometrySize = 12,
                LineSmoothness = 0
            }
        };
    }

    private async Task LoadSalaryRangeChart()
    {
        var employees = await _employeeService.FindAllAsync();
        var salaryRanges = new[]
        {
            new { Range = "3000以下", Min = 0m, Max = 3000m },
            new { Range = "3000-5000", Min = 3000m, Max = 5000m },
            new { Range = "5000-8000", Min = 5000m, Max = 8000m },
            new { Range = "8000-12000", Min = 8000m, Max = 12000m },
            new { Range = "12000以上", Min = 12000m, Max = decimal.MaxValue }
        };

        var rangeCounts = salaryRanges.Select(range => new
        {
            Range = range.Range,
            Count = employees.Count(e => e.BaseSalary >= range.Min && e.BaseSalary < range.Max)
        }).ToList();

        SalaryRangeChart = new ObservableCollection<ISeries>
        {
            new ColumnSeries<double>
            {
                Name = "员工数量",
                Values = rangeCounts.Select(x => (double)x.Count).ToArray()
            }
        };
    }

    private async Task LoadMonthlyPayrollChart()
    {
        // 由于PayrollManagementService没有GetAllPayrollsAsync方法，我们需要用另一种方式获取数据
        // 这里可以使用GetPayrollsByMonthAsync方法获取最近几个月的数据
        var monthlyData = new List<(string Month, double TotalSalary)>();
        
        // 获取最近12个月的数据
        for (int i = 11; i >= 0; i--)
        {
            var targetDate = DateTime.Now.AddMonths(-i);
            var payMonth = targetDate.ToString("yyyy-MM");
            
            try
            {
                var payrolls = await _payrollService.GetPayrollsByMonthAsync(payMonth, 1, 1000);
                var totalSalary = payrolls.Sum(p => (double)p.NetSalary);
                monthlyData.Add((payMonth, totalSalary));
            }
            catch
            {
                // 如果获取失败，添加0值
                monthlyData.Add((payMonth, 0));
            }
        }

        MonthlyPayrollChart = new ObservableCollection<ISeries>
        {
            new LineSeries<double>
            {
                Name = "总薪资支出",
                Values = monthlyData.Select(x => x.TotalSalary).ToArray(),
                GeometrySize = 12,
                LineSmoothness = 0.5,
                Stroke = new SolidColorPaint(SKColors.Blue) { StrokeThickness = 3 }
            }
        };
    }

    public async Task ExportDepartmentDistributionToCSV()
    {
        var employees = await _employeeService.FindAllAsync();
        var departmentGroups = employees.GroupBy(e => e.Department ?? "未分配")
            .Select(g => new { Department = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .ToList();

        var csv = new StringBuilder();
        csv.AppendLine("部门,员工数量");
        foreach (var item in departmentGroups)
        {
            csv.AppendLine($"{item.Department},{item.Count}");
        }

        await SaveCSVFile(csv.ToString(), "部门人员分布.csv");
    }

    public async Task ExportSalaryTrendToCSV()
    {
        var employees = await _employeeService.FindAllAsync();
        var salaryByDepartment = employees.GroupBy(e => e.Department ?? "未分配")
            .Select(g => new { Department = g.Key, AvgSalary = g.Average(e => (double)e.BaseSalary) })
            .OrderBy(x => x.Department)
            .ToList();

        var csv = new StringBuilder();
        csv.AppendLine("部门,平均薪资");
        foreach (var item in salaryByDepartment)
        {
            csv.AppendLine($"{item.Department},{item.AvgSalary:F2}");
        }

        await SaveCSVFile(csv.ToString(), "部门平均薪资.csv");
    }

    public async Task ExportAttendanceStatusToCSV()
    {
        var attendances = await _attendanceService.FindAllAsync();
        var statusGroups = attendances.GroupBy(a => a.Status ?? "未知")
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToList();

        var csv = new StringBuilder();
        csv.AppendLine("考勤状态,数量");
        foreach (var item in statusGroups)
        {
            csv.AppendLine($"{item.Status},{item.Count}");
        }

        await SaveCSVFile(csv.ToString(), "考勤状态统计.csv");
    }

    public async Task ExportRewardPunishmentToCSV()
    {
        var records = await _rewardPunishmentService.FindAllAsync();
        var typeGroups = records.GroupBy(r => r.Type)
            .Select(g => new { Type = g.Key, Count = g.Count(), TotalAmount = g.Sum(r => (double)r.Amount) })
            .ToList();

        var csv = new StringBuilder();
        csv.AppendLine("类型,数量,总金额");
        foreach (var item in typeGroups)
        {
            csv.AppendLine($"{item.Type},{item.Count},{item.TotalAmount:F2}");
        }

        await SaveCSVFile(csv.ToString(), "奖惩统计.csv");
    }

    public async Task ExportSalaryRangeToCSV()
    {
        var employees = await _employeeService.FindAllAsync();
        var salaryRanges = new[]
        {
            new { Range = "3000以下", Min = 0m, Max = 3000m },
            new { Range = "3000-5000", Min = 3000m, Max = 5000m },
            new { Range = "5000-8000", Min = 5000m, Max = 8000m },
            new { Range = "8000-12000", Min = 8000m, Max = 12000m },
            new { Range = "12000以上", Min = 12000m, Max = decimal.MaxValue }
        };

        var rangeCounts = salaryRanges.Select(range => new
        {
            Range = range.Range,
            Count = employees.Count(e => e.BaseSalary >= range.Min && e.BaseSalary < range.Max)
        }).ToList();

        var csv = new StringBuilder();
        csv.AppendLine("薪资范围,员工数量");
        foreach (var item in rangeCounts)
        {
            csv.AppendLine($"{item.Range},{item.Count}");
        }

        await SaveCSVFile(csv.ToString(), "薪资分布.csv");
    }

    public async Task ExportMonthlyPayrollToCSV()
    {
        var monthlyData = new List<(string Month, double TotalSalary)>();
        
        // 获取最近12个月的数据
        for (int i = 11; i >= 0; i--)
        {
            var targetDate = DateTime.Now.AddMonths(-i);
            var payMonth = targetDate.ToString("yyyy-MM");
            
            try
            {
                var payrolls = await _payrollService.GetPayrollsByMonthAsync(payMonth, 1, 1000);
                var totalSalary = payrolls.Sum(p => (double)p.NetSalary);
                monthlyData.Add((payMonth, totalSalary));
            }
            catch
            {
                monthlyData.Add((payMonth, 0));
            }
        }

        var csv = new StringBuilder();
        csv.AppendLine("月份,总薪资支出");
        foreach (var item in monthlyData)
        {
            csv.AppendLine($"{item.Month},{item.TotalSalary:F2}");
        }

        await SaveCSVFile(csv.ToString(), "月度薪资支出.csv");
    }

    private async Task SaveCSVFile(string content, string filename)
    {
        try
        {
            var downloadsPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            var desktopPath = Path.Combine(downloadsPath, "Desktop");
            var filePath = Path.Combine(desktopPath, filename);

            await File.WriteAllTextAsync(filePath, content, Encoding.UTF8);
            
            // 这里可以添加通知用户文件已保存的逻辑
            Console.WriteLine($"CSV文件已保存到: {filePath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"保存CSV文件时出错: {ex.Message}");
        }
    }
}
