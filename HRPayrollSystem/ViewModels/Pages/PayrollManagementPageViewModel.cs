using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;
using System.Linq;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using System.IO;
using System.Collections.Generic;
using Avalonia.Platform.Storage;
using HRPayrollSystem.Views.Dialogs;
using System.Reactive.Linq;

namespace HRPayrollSystem.ViewModels.Pages
{
    public class PayrollManagementPageViewModel : ViewModelBase
    {
        private readonly PayrollManagementService _payrollManagementService;
        private readonly RewardPunishmentManagementService _rewardPunishmentService;
        private readonly EmployeeManagementService _employeeManagementService;
        private readonly AttendanceManagementService _attendanceManagementService;
        private bool _isLoading;

        public ICommand GeneratePayrollsCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ConfirmPayrollCommand { get; }
        public ICommand BatchConfirmCommand { get; }
        public ICommand ExportPayrollsCommand { get; }
        public ICommand ShowDetailsCommand { get; }
        
        // Pagination Commands
        public ICommand FirstPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand LastPageCommand { get; }
        public ICommand GoToPageCommand { get; }

        private int _startYear = DateTime.Now.Year;
        public int StartYear
        {
            get => _startYear;
            set => this.RaiseAndSetIfChanged(ref _startYear, value);
        }

        private int _startMonth = DateTime.Now.Month;
        public int StartMonth
        {
            get => _startMonth;
            set => this.RaiseAndSetIfChanged(ref _startMonth, value);
        }
        
        private int _endYear = DateTime.Now.Year;
        public int EndYear
        {
            get => _endYear;
            set => this.RaiseAndSetIfChanged(ref _endYear, value);
        }

        private int _endMonth = DateTime.Now.Month;
        public int EndMonth
        {
            get => _endMonth;
            set => this.RaiseAndSetIfChanged(ref _endMonth, value);
        }

        private string _searchText = "";
        public string SearchText
        {
            get => _searchText;
            set => this.RaiseAndSetIfChanged(ref _searchText, value);
        }
        
        private Payroll? _selectedPayroll;
        public Payroll? SelectedPayroll
        {
            get => _selectedPayroll;
            set => this.RaiseAndSetIfChanged(ref _selectedPayroll, value);
        }
        
        private bool _isGenerating;
        public bool IsGenerating
        {
            get => _isGenerating;
            set => this.RaiseAndSetIfChanged(ref _isGenerating, value);
        }

        // Pagination Properties
        private int _currentPage = 1;
        public int CurrentPage
        {
            get => _currentPage;
            set => this.RaiseAndSetIfChanged(ref _currentPage, value);
        }

        private int _pageSize = 20;
        public int PageSize
        {
            get => _pageSize;
            set => this.RaiseAndSetIfChanged(ref _pageSize, value);
        }

        private int _totalPages = 1;
        public int TotalPages
        {
            get => _totalPages;
            set => this.RaiseAndSetIfChanged(ref _totalPages, value);
        }

        private int _totalItems = 0;
        public int TotalItems
        {
            get => _totalItems;
            set => this.RaiseAndSetIfChanged(ref _totalItems, value);
        }

        private bool _isFirstPage = true;
        public bool IsFirstPage
        {
            get => _isFirstPage;
            set => this.RaiseAndSetIfChanged(ref _isFirstPage, value);
        }

        private bool _isLastPage = true;
        public bool IsLastPage
        {
            get => _isLastPage;
            set => this.RaiseAndSetIfChanged(ref _isLastPage, value);
        }

        private string _pageSizeText = "20";
        public string PageSizeText
        {
            get => _pageSizeText;
            set => this.RaiseAndSetIfChanged(ref _pageSizeText, value);
        }

        private string _goToPageText = "1";
        public string GoToPageText
        {
            get => _goToPageText;
            set => this.RaiseAndSetIfChanged(ref _goToPageText, value);
        }

        private ObservableCollection<int> _years = new();
        public ObservableCollection<int> Years
        {
            get => _years;
            set => this.RaiseAndSetIfChanged(ref _years, value);
        }

        private ObservableCollection<int> _months = new();
        public ObservableCollection<int> Months
        {
            get => _months;
            set => this.RaiseAndSetIfChanged(ref _months, value);
        }

        public ObservableCollection<Payroll> Payrolls { get; } = new();
        
        public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };

        public PayrollManagementPageViewModel(
            PayrollManagementService payrollManagementService,
            RewardPunishmentManagementService rewardPunishmentService,
            EmployeeManagementService employeeManagementService,
            AttendanceManagementService attendanceManagementService)
        {
            _payrollManagementService = payrollManagementService;
            _rewardPunishmentService = rewardPunishmentService;
            _employeeManagementService = employeeManagementService;
            _attendanceManagementService = attendanceManagementService;
            
            // 初始化年份列表 (当前年份及前5年)
            for (int i = 0; i >= -5; i--)
            {
                Years.Add(DateTime.Now.Year + i);
            }
            
            // 初始化月份列表 (1-12月)
            for (int i = 1; i <= 12; i++)
            {
                Months.Add(i);
            }
            
            GeneratePayrollsCommand = ReactiveCommand.CreateFromTask(GeneratePayrollsAsync);
            SearchCommand = ReactiveCommand.CreateFromTask(LoadPayrollsAsync);
            ConfirmPayrollCommand = ReactiveCommand.CreateFromTask<Payroll>(ConfirmSinglePayrollAsync);
            BatchConfirmCommand = ReactiveCommand.CreateFromTask(ConfirmSelectedPayrollsAsync);
            ExportPayrollsCommand = ReactiveCommand.CreateFromTask(ExportPayrollsAsync);
            ShowDetailsCommand = ReactiveCommand.CreateFromTask<Payroll>(ShowPayrollDetailsAsync);
            
            FirstPageCommand = ReactiveCommand.CreateFromTask(GoToFirstPageAsync);
            PreviousPageCommand = ReactiveCommand.CreateFromTask(GoToPreviousPageAsync);
            NextPageCommand = ReactiveCommand.CreateFromTask(GoToNextPageAsync);
            LastPageCommand = ReactiveCommand.CreateFromTask(GoToLastPageAsync);
            GoToPageCommand = ReactiveCommand.CreateFromTask(GoToSpecificPageAsync);
            
            this.WhenAnyValue(x => x.PageSize)
                .Subscribe(async _ => await OnPageSizeChangedAsync());
                
            // 当年份或月份变化时，自动加载数据
            this.WhenAnyValue(x => x.StartYear, x => x.StartMonth, x => x.EndYear, x => x.EndMonth)
                .Subscribe(async _ => await LoadPayrollsAsync());
                
            // 订阅奖惩变更事件以实时刷新工资单
            _rewardPunishmentService.RewardPunishmentChanged += async (sender, args) =>
            {
                await RefreshPayrollsAsync();
            };
            
            // 订阅员工基本工资变更事件
            _employeeManagementService.EmployeeUpdated += async (sender, args) =>
            {
                await RefreshPayrollsAsync();
            };
            
            // 订阅考勤变更事件
            _attendanceManagementService.AttendanceChanged += async (sender, args) =>
            {
                await RefreshPayrollsAsync();
            };
            
            _ = LoadPayrollsAsync();
        }

        private async Task GeneratePayrollsAsync()
        {
            if (IsGenerating)
                return;
                
            IsGenerating = true;
            
            try
            {
                // 格式化月份为 "YYYY-MM"
                string payMonth = $"{StartYear}-{StartMonth:D2}";
                
                // 显示确认对话框
                var confirmDialog = new ContentDialog
                {
                    Title = "确认操作",
                    Content = $"确定要为 {payMonth} 生成工资单吗？如果已存在该月工资单的员工将被跳过。",
                    PrimaryButtonText = "确定",
                    CloseButtonText = "取消"
                };
                
                var result = await confirmDialog.ShowAsync();
                if (result != ContentDialogResult.Primary)
                    return;
                
                // 执行生成工资单操作
                var success = await _payrollManagementService.GenerateMonthlyPayrollsAsync(payMonth);
                
                if (success)
                {
                    // 生成成功后刷新工资单列表，确保奖惩最新
                    await RefreshPayrollsAsync();
                    
                    // 显示结果
                    var resultDialog = new ContentDialog
                    {
                        Title = "操作成功",
                        Content = $"{payMonth} 月工资单生成成功！",
                        PrimaryButtonText = "确定"
                    };
                    
                    await resultDialog.ShowAsync();
                }
                else
                {
                    // 显示失败结果
                    var resultDialog = new ContentDialog
                    {
                        Title = "操作失败",
                        Content = $"{payMonth} 月工资单生成失败，请查看日志。",
                        PrimaryButtonText = "确定"
                    };
                    
                    await resultDialog.ShowAsync();
                }
            }
            catch (Exception ex)
            {
                var errorDialog = new ContentDialog
                {
                    Title = "错误",
                    Content = $"生成工资单时发生错误: {ex.Message}",
                    PrimaryButtonText = "确定"
                };
                
                await errorDialog.ShowAsync();
            }
            finally
            {
                IsGenerating = false;
            }
        }
        
        // 刷新工资单，所有变更将实时刷新所有月份范围内的工资单
        private async Task RefreshPayrollsAsync()
        {
            try
            {
                // 遍历日期范围内的所有月份
                var currentDate = new DateTime(StartYear, StartMonth, 1);
                var endDate = new DateTime(EndYear, EndMonth, 1);
                
                while (currentDate <= endDate)
                {
                    string payMonth = $"{currentDate.Year}-{currentDate.Month:D2}";
                    
                    // 刷新工资单中的奖惩变动、考勤变动并重新计算实发工资
                    await _payrollManagementService.RefreshPayrollsAsync(payMonth);
                    
                    // 移到下一个月
                    currentDate = currentDate.AddMonths(1);
                }
                
                // 刷新界面显示
                await LoadPayrollsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"刷新工资单时发生错误: {ex.Message}");
            }
        }
        
        private async Task LoadPayrollsAsync()
        {
            if (_isLoading) return;
            _isLoading = true;

            Payrolls.Clear();
            
            try
            {
                // 获取所有指定日期范围内的工资单
                var currentDate = new DateTime(StartYear, StartMonth, 1);
                var endDate = new DateTime(EndYear, EndMonth, 1);
                
                var allPayrolls = new List<Payroll>();
                
                while (currentDate <= endDate)
                {
                    string payMonth = $"{currentDate.Year}-{currentDate.Month:D2}";
                    
                    // 获取当前月份的工资单
                    var monthPayrolls = await _payrollManagementService.SearchPayrollsAsync(
                        SearchText, payMonth, 1, 1000); // 使用较大的页面大小以获取所有数据
                    
                    allPayrolls.AddRange(monthPayrolls);
                    
                    // 移到下一个月
                    currentDate = currentDate.AddMonths(1);
                }
                
                // 手动分页
                int totalCount = allPayrolls.Count;
                var pagedPayrolls = allPayrolls
                    .Skip((CurrentPage - 1) * PageSize)
                    .Take(PageSize)
                    .ToList();
                
                // 更新分页信息
                TotalItems = totalCount;
                TotalPages = (int)Math.Ceiling((double)totalCount / PageSize);
                UpdatePaginationState();
                
                // 更新UI
                foreach (var payroll in pagedPayrolls)
                {
                    Payrolls.Add(payroll);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载工资单时发生错误: {ex.Message}");
            }
            finally
            {
                _isLoading = false;
            }
        }
        
        private async Task ShowPayrollDetailsAsync(Payroll payroll)
        {
            try
            {
                var dialog = new ContentDialog
                {
                    Title = "工资单详情",
                    PrimaryButtonText = "关闭",
                    DefaultButton = ContentDialogButton.Primary
                };
                
                var content = new ShowPayrollDialogContent();
                dialog.Content = content;
                
                // 加载数据
                if (content.DataContext is ViewModels.Dialogs.ShowPayrollDialogContentViewModel vm)
                {
                    await vm.LoadPayrollAsync(payroll.Id);
                }
                
                await dialog.ShowAsync();
            }
            catch (Exception ex)
            {
                var errorDialog = new ContentDialog
                {
                    Title = "错误",
                    Content = $"显示工资单详情时发生错误: {ex.Message}",
                    PrimaryButtonText = "确定"
                };
                
                await errorDialog.ShowAsync();
            }
        }
        
        private async Task ConfirmSinglePayrollAsync(Payroll payroll)
        {
            // 显示确认对话框
            var confirmDialog = new ContentDialog
            {
                Title = "确认操作",
                Content = $"确认将 {payroll.EmployeeName} ({payroll.PayMonth}) 的工资状态修改为已发放吗？",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消"
            };
            
            var result = await confirmDialog.ShowAsync();
            if (result != ContentDialogResult.Primary)
                return;
            
            // 更新状态
            var success = await _payrollManagementService.UpdatePayrollStatusAsync(payroll.Id, "已发放");
            
            if (success)
            {
                await LoadPayrollsAsync();
            }
            else
            {
                var errorDialog = new ContentDialog
                {
                    Title = "错误",
                    Content = "确认工资发放失败！",
                    PrimaryButtonText = "确定"
                };
                
                await errorDialog.ShowAsync();
            }
        }
        
        private async Task ConfirmSelectedPayrollsAsync()
        {
            // 获取所有待确认的工资单
            var payrollsToConfirm = Payrolls.Where(p => p.Status == "待确认").ToList();
            
            if (!payrollsToConfirm.Any())
            {
                var noItemsDialog = new ContentDialog
                {
                    Title = "提示",
                    Content = "没有待确认的工资单！",
                    PrimaryButtonText = "确定"
                };
                
                await noItemsDialog.ShowAsync();
                return;
            }
            
            // 显示确认对话框
            var confirmDialog = new ContentDialog
            {
                Title = "确认操作",
                Content = $"确认将所有待确认的 {payrollsToConfirm.Count} 条工资记录状态修改为已发放吗？",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消"
            };
            
            var result = await confirmDialog.ShowAsync();
            if (result != ContentDialogResult.Primary)
                return;
            
            // 批量更新状态
            var ids = payrollsToConfirm.Select(p => p.Id).ToList();
            var success = await _payrollManagementService.UpdatePayrollStatusBatchAsync(ids, "已发放");
            
            if (success)
            {
                await LoadPayrollsAsync();
                
                var successDialog = new ContentDialog
                {
                    Title = "操作成功",
                    Content = "批量确认工资发放成功！",
                    PrimaryButtonText = "确定"
                };
                
                await successDialog.ShowAsync();
            }
            else
            {
                var errorDialog = new ContentDialog
                {
                    Title = "错误",
                    Content = "批量确认工资发放失败！",
                    PrimaryButtonText = "确定"
                };
                
                await errorDialog.ShowAsync();
            }
        }
        
        private async Task ExportPayrollsAsync()
        {
            try
            {
                string payMonth = $"{StartYear}-{StartMonth:D2}";
                
                // 自动导出到Excel文件
                var filePath = await _payrollManagementService.ExportPayrollsToExcelAsync(payMonth);
                
                if (string.IsNullOrEmpty(filePath))
                {
                    var errorDialog = new ContentDialog
                    {
                        Title = "错误",
                        Content = "导出工资单数据失败！",
                        PrimaryButtonText = "确定"
                    };
                    
                    await errorDialog.ShowAsync();
                    return;
                }
                
                // 显示导出成功对话框，提供三个选项
                var resultDialog = new ContentDialog
                {
                    Title = "导出成功",
                    Content = $"工资单数据已成功导出到:\n{filePath}\n\n是否立即打开该文件？",
                    PrimaryButtonText = "打开文件",
                    SecondaryButtonText = "打开所在文件夹",
                    CloseButtonText = "关闭"
                };
                
                var result = await resultDialog.ShowAsync();
                
                if (result == ContentDialogResult.Primary)
                {
                    // 打开文件
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else if (result == ContentDialogResult.Secondary)
                {
                    // 打开所在文件夹
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = Path.GetDirectoryName(filePath),
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                var errorDialog = new ContentDialog
                {
                    Title = "错误",
                    Content = $"导出工资单时发生错误: {ex.Message}",
                    PrimaryButtonText = "确定"
                };
                
                await errorDialog.ShowAsync();
            }
        }
        
        private void UpdatePaginationState()
        {
            IsFirstPage = CurrentPage <= 1;
            IsLastPage = CurrentPage >= TotalPages;
            GoToPageText = CurrentPage.ToString();
        }

        private async Task GoToFirstPageAsync()
        {
            if (CurrentPage != 1)
            {
                CurrentPage = 1;
                await RefreshCurrentPageAsync();
            }
        }

        private async Task GoToPreviousPageAsync()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                await RefreshCurrentPageAsync();
            }
        }

        private async Task GoToNextPageAsync()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                await RefreshCurrentPageAsync();
            }
        }

        private async Task GoToLastPageAsync()
        {
            if (CurrentPage != TotalPages && TotalPages > 0)
            {
                CurrentPage = TotalPages;
                await RefreshCurrentPageAsync();
            }
        }

        private async Task GoToSpecificPageAsync()
        {
            if (int.TryParse(GoToPageText, out int targetPage))
            {
                if (targetPage >= 1 && targetPage <= TotalPages && targetPage != CurrentPage)
                {
                    CurrentPage = targetPage;
                    await RefreshCurrentPageAsync();
                    return;
                }
                if (targetPage == CurrentPage)
                {
                    return;
                }
            }
            
            // 无效的页码，重置为当前页码
            GoToPageText = CurrentPage.ToString();
        }
        
        private async Task RefreshCurrentPageAsync()
        {
            await LoadPayrollsAsync();
        }
        
        private async Task OnPageSizeChangedAsync()
        {
            CurrentPage = 1; // 重置到第一页
            await LoadPayrollsAsync();
        }
    }
} 