using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.Views.Dialogs;
using System.Linq;

namespace HRPayrollSystem.ViewModels.Pages;

public partial class DepartmentManagementPageViewModel : ViewModelBase
{
    private readonly DepartmentManagementService _departmentManagementService;
    private bool _isLoading;

    public ICommand ShowAddDepartmentDialogCommand { get; }
    public ICommand RefreshCommand { get; }
    public ICommand DeleteDepartmentCommand { get; }
    public ICommand EditDepartmentCommand { get; }
    public ICommand SearchCommand { get; }
    private string _searchText = "";
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }

    public ObservableCollection<Department> Departments { get; } = new();

    public ICommand FirstPageCommand { get; }
    public ICommand PreviousPageCommand { get; }
    public ICommand NextPageCommand { get; }
    public ICommand LastPageCommand { get; }
    public ICommand GoToPageCommand { get; }

    private int _currentPage = 1;
    public int CurrentPage
    {
        get => _currentPage;
        set => this.RaiseAndSetIfChanged(ref _currentPage, value);
    }

    private int _pageSize = 20;
    public int PageSize
    {
        get => _pageSize;
        set => this.RaiseAndSetIfChanged(ref _pageSize, value);
    }

    private int _totalPages = 1;
    public int TotalPages
    {
        get => _totalPages;
        set => this.RaiseAndSetIfChanged(ref _totalPages, value);
    }

    private int _totalItems = 0;
    public int TotalItems
    {
        get => _totalItems;
        set => this.RaiseAndSetIfChanged(ref _totalItems, value);
    }

    private bool _isFirstPage = true;
    public bool IsFirstPage
    {
        get => _isFirstPage;
        set => this.RaiseAndSetIfChanged(ref _isFirstPage, value);
    }

    private bool _isLastPage = true;
    public bool IsLastPage
    {
        get => _isLastPage;
        set => this.RaiseAndSetIfChanged(ref _isLastPage, value);
    }

    private string _pageSizeText = "20";
    public string PageSizeText
    {
        get => _pageSizeText;
        set => this.RaiseAndSetIfChanged(ref _pageSizeText, value);
    }

    private string _goToPageText = "1";
    public string GoToPageText
    {
        get => _goToPageText;
        set => this.RaiseAndSetIfChanged(ref _goToPageText, value);
    }

    public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };

    public DepartmentManagementPageViewModel(DepartmentManagementService departmentManagementService)
    {
        _departmentManagementService = departmentManagementService;
        ShowAddDepartmentDialogCommand = ReactiveCommand.CreateFromTask(ShowAddDepartmentDialogAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(LoadDepartmentsAsync);
        DeleteDepartmentCommand = ReactiveCommand.CreateFromTask<Department>(DeleteByIdAsync);
        EditDepartmentCommand = ReactiveCommand.CreateFromTask<Department>(ShowEditDepartmentDialogAsync);
        SearchCommand = ReactiveCommand.CreateFromTask(SearchDepartmentsAsync);
        FirstPageCommand = ReactiveCommand.CreateFromTask(GoToFirstPageAsync);
        PreviousPageCommand = ReactiveCommand.CreateFromTask(GoToPreviousPageAsync);
        NextPageCommand = ReactiveCommand.CreateFromTask(GoToNextPageAsync);
        LastPageCommand = ReactiveCommand.CreateFromTask(GoToLastPageAsync);
        GoToPageCommand = ReactiveCommand.CreateFromTask(GoToSpecificPageAsync);

        this.WhenAnyValue(x => x.PageSize)
            .Subscribe(async _ => await OnPageSizeChangedAsync());

        _ = LoadDepartmentsAsync();
    }

    private async Task DeleteByIdAsync(Department department)
    {
        var deleted = await _departmentManagementService.DeleteDepartmentAsync(department.Id);
        if (deleted)
        {
            Console.WriteLine(department.ToString());
            Departments.Remove(department);
        }
    }

    private async Task ShowAddDepartmentDialogAsync()
    {
        var dialog = new ContentDialog()
        {
            Title = "添加部门",
            PrimaryButtonText = "添加",
            SecondaryButtonText = "清空",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new AddDepartmentDialogContentViewModel(dialog, _departmentManagementService);
        dialog.Content = new AddDepartmentDialogContent()
        {
            DataContext = viewModel
        };

        await dialog.ShowAsync();
        await LoadDepartmentsAsync();
    }

    private async Task LoadDepartmentsAsync()
    {
        if (_isLoading) return;
        _isLoading = true;

        Departments.Clear();
        var departmentsFromDb = await _departmentManagementService.FindAllAsync();
        TotalItems = departmentsFromDb.Count;
        TotalPages = (int)Math.Ceiling((double)TotalItems / PageSize);
        if (CurrentPage < 1) CurrentPage = 1;
        if (CurrentPage > TotalPages) CurrentPage = TotalPages;
        var paged = departmentsFromDb.Skip((CurrentPage - 1) * PageSize).Take(PageSize);
        foreach (var department in paged)
        {
            Departments.Add(department);
        }
        IsFirstPage = CurrentPage == 1;
        IsLastPage = CurrentPage == TotalPages || TotalPages == 0;
        GoToPageText = CurrentPage.ToString();
        _isLoading = false;
    }

    private async Task OnPageSizeChangedAsync()
    {
        CurrentPage = 1;
        await LoadDepartmentsAsync();
    }

    private async Task GoToFirstPageAsync()
    {
        if (!IsFirstPage)
        {
            CurrentPage = 1;
            await LoadDepartmentsAsync();
        }
    }

    private async Task GoToPreviousPageAsync()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            await LoadDepartmentsAsync();
        }
    }

    private async Task GoToNextPageAsync()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            await LoadDepartmentsAsync();
        }
    }

    private async Task GoToLastPageAsync()
    {
        if (!IsLastPage)
        {
            CurrentPage = TotalPages;
            await LoadDepartmentsAsync();
        }
    }
    
    private async Task GoToSpecificPageAsync()
    {
        if (int.TryParse(GoToPageText, out int targetPage))
        {
            if (targetPage >= 1 && targetPage <= TotalPages && targetPage != CurrentPage)
            {
                CurrentPage = targetPage;
                await RefreshCurrentPageAsync();
                return;
            }
            if (targetPage == CurrentPage)
            {
                return;
            }
        }
        GoToPageText = CurrentPage.ToString();
        var dialog = new ContentDialog()
        {
            Title = "错误",
            Content = "请输入有效的页码！",
            PrimaryButtonText = "好的",
            DefaultButton = ContentDialogButton.Primary
        };
        await dialog.ShowAsync();
    }
    private async Task ShowEditDepartmentDialogAsync(Department department)
    {
        var dialog = new ContentDialog()
        {
            Title = "编辑部门",
            PrimaryButtonText = "保存",
            SecondaryButtonText = "重置",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new EditDepartmentDialogContentViewModel(dialog, _departmentManagementService, department);
        dialog.Content = new EditDepartmentDialogContent()
        {
            DataContext = viewModel
        };

        await dialog.ShowAsync();
        await LoadDepartmentsAsync();
    }

    private async Task SearchDepartmentsAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadDepartmentsAsync();
            return;
        }
        Departments.Clear();
        var result = await _departmentManagementService.SearchDepartmentsAsync(SearchText);
        if (result.Count == 0)
        {
            var dialog = new FluentAvalonia.UI.Controls.ContentDialog()
            {
                Title = "提示",
                Content = "没有查询到相关部门！",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            await dialog.ShowAsync();
        }
        else
        {
            foreach (var d in result)
                Departments.Add(d);
        }
    }
    private async Task RefreshCurrentPageAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadDepartmentsAsync();
        }
        else
        {
            await SearchDepartmentsAsync();
        }
    }
} 