using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.Views.Dialogs;
using System.Linq;

namespace HRPayrollSystem.ViewModels.Pages;

public partial class RewardPunishmentManagementPageViewModel : ViewModelBase
{
    private readonly RewardPunishmentManagementService _rewardPunishmentManagementService;
    private readonly UserManagementService _userManagementService;
    private bool _isLoading;

    public ICommand ShowAddRewardPunishmentDialogCommand { get; }
    public ICommand RefreshCommand { get; }
    public ICommand DeleteRewardPunishmentCommand { get; }
    public ICommand EditRewardPunishmentCommand { get; }
    public ReactiveCommand<RewardPunishment, Unit> ShowDetailsCommand { get; }
    public ReactiveCommand<Unit, Unit> SearchCommand { get; }
    public ICommand FirstPageCommand { get; }
    public ICommand PreviousPageCommand { get; }
    public ICommand NextPageCommand { get; }
    public ICommand LastPageCommand { get; }
    public ICommand GoToPageCommand { get; }

    private string _searchText = "";
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }

    private int _currentPage = 1;
    public int CurrentPage
    {
        get => _currentPage;
        set => this.RaiseAndSetIfChanged(ref _currentPage, value);
    }

    private int _pageSize = 20;
    public int PageSize
    {
        get => _pageSize;
        set => this.RaiseAndSetIfChanged(ref _pageSize, value);
    }

    private int _totalPages = 1;
    public int TotalPages
    {
        get => _totalPages;
        set => this.RaiseAndSetIfChanged(ref _totalPages, value);
    }

    private int _totalItems = 0;
    public int TotalItems
    {
        get => _totalItems;
        set => this.RaiseAndSetIfChanged(ref _totalItems, value);
    }

    private bool _isFirstPage = true;
    public bool IsFirstPage
    {
        get => _isFirstPage;
        set => this.RaiseAndSetIfChanged(ref _isFirstPage, value);
    }

    private bool _isLastPage = true;
    public bool IsLastPage
    {
        get => _isLastPage;
        set => this.RaiseAndSetIfChanged(ref _isLastPage, value);
    }

    private string _pageSizeText = "20";
    public string PageSizeText
    {
        get => _pageSizeText;
        set => this.RaiseAndSetIfChanged(ref _pageSizeText, value);
    }

    private string _goToPageText = "1";
    public string GoToPageText
    {
        get => _goToPageText;
        set => this.RaiseAndSetIfChanged(ref _goToPageText, value);
    }

    public ObservableCollection<RewardPunishment> RewardPunishments { get; } = new();
    public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };

    public RewardPunishmentManagementPageViewModel(RewardPunishmentManagementService rewardPunishmentManagementService, UserManagementService userManagementService)
    {
        _rewardPunishmentManagementService = rewardPunishmentManagementService;
        _userManagementService = userManagementService;

        ShowAddRewardPunishmentDialogCommand = ReactiveCommand.CreateFromTask(ShowAddRewardPunishmentDialogAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(LoadRewardPunishmentsAsync);
        DeleteRewardPunishmentCommand = ReactiveCommand.CreateFromTask<RewardPunishment>(DeleteByIdAsync);
        EditRewardPunishmentCommand = ReactiveCommand.CreateFromTask<RewardPunishment>(ShowEditRewardPunishmentDialogAsync);
        ShowDetailsCommand = ReactiveCommand.CreateFromTask<RewardPunishment>(ShowDetailsDialogAsync);
        SearchCommand = ReactiveCommand.CreateFromTask(SearchRewardPunishmentsAsync);
        FirstPageCommand = ReactiveCommand.CreateFromTask(GoToFirstPageAsync);
        PreviousPageCommand = ReactiveCommand.CreateFromTask(GoToPreviousPageAsync);
        NextPageCommand = ReactiveCommand.CreateFromTask(GoToNextPageAsync);
        LastPageCommand = ReactiveCommand.CreateFromTask(GoToLastPageAsync);
        GoToPageCommand = ReactiveCommand.CreateFromTask(GoToSpecificPageAsync);

        this.WhenAnyValue(x => x.PageSize)
            .Subscribe(async _ => await OnPageSizeChangedAsync());

        _ = LoadRewardPunishmentsAsync();
    }

    private async Task DeleteByIdAsync(RewardPunishment rewardPunishment)
    {
        var deleted = await _rewardPunishmentManagementService.DeleteRewardPunishmentAsync(rewardPunishment.Id);
        if (deleted)
        {
            RewardPunishments.Remove(rewardPunishment);
        }
    }

    private async Task ShowAddRewardPunishmentDialogAsync()
    {
        var dialog = new ContentDialog()
        {
            Title = "添加奖罚记录",
            PrimaryButtonText = "添加",
            SecondaryButtonText = "清空",
            CloseButtonText = "取消"
        };

        var viewModel = new AddRewardPunishmentDialogContentViewModel(dialog, _rewardPunishmentManagementService, _userManagementService);
        dialog.Content = new AddRewardPunishmentDialogContent()
        {
            DataContext = viewModel
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            await LoadRewardPunishmentsAsync();
        }
    }

    private async Task LoadRewardPunishmentsAsync()
    {
        if (_isLoading) return;
        _isLoading = true;

        RewardPunishments.Clear();
        var rewardPunishmentsFromDb = await _rewardPunishmentManagementService.FindAllAsync();
        TotalItems = rewardPunishmentsFromDb.Count;
        TotalPages = (int)Math.Ceiling((double)TotalItems / PageSize);
        if (CurrentPage < 1) CurrentPage = 1;
        if (CurrentPage > TotalPages) CurrentPage = TotalPages;
        var paged = rewardPunishmentsFromDb.Skip((CurrentPage - 1) * PageSize).Take(PageSize);
        foreach (var rewardPunishment in paged)
        {
            RewardPunishments.Add(rewardPunishment);
        }
        IsFirstPage = CurrentPage == 1;
        IsLastPage = CurrentPage == TotalPages || TotalPages == 0;
        GoToPageText = CurrentPage.ToString();
        _isLoading = false;
    }

    private async Task OnPageSizeChangedAsync()
    {
        CurrentPage = 1;
        await LoadRewardPunishmentsAsync();
    }

    private async Task GoToFirstPageAsync()
    {
        if (!IsFirstPage)
        {
            CurrentPage = 1;
            await LoadRewardPunishmentsAsync();
        }
    }

    private async Task GoToPreviousPageAsync()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            await LoadRewardPunishmentsAsync();
        }
    }

    private async Task GoToNextPageAsync()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            await LoadRewardPunishmentsAsync();
        }
    }

    private async Task GoToLastPageAsync()
    {
        if (!IsLastPage)
        {
            CurrentPage = TotalPages;
            await LoadRewardPunishmentsAsync();
        }
    }

    private async Task GoToSpecificPageAsync()
    {
        if (int.TryParse(GoToPageText, out int targetPage))
        {
            if (targetPage >= 1 && targetPage <= TotalPages && targetPage != CurrentPage)
            {
                CurrentPage = targetPage;
                await RefreshCurrentPageAsync();
                return;
            }
            if (targetPage == CurrentPage)
            {
                return;
            }
        }
        GoToPageText = CurrentPage.ToString();
        var dialog = new ContentDialog()
        {
            Title = "错误",
            Content = "请输入有效的页码！",
            PrimaryButtonText = "好的",
            DefaultButton = ContentDialogButton.Primary
        };
        await dialog.ShowAsync();
    }
    
    private async Task ShowEditRewardPunishmentDialogAsync(RewardPunishment rewardPunishment)
    {
        var dialog = new ContentDialog()
        {
            Title = "编辑奖罚记录",
            PrimaryButtonText = "保存",
            SecondaryButtonText = "重置",
            CloseButtonText = "取消"
        };

        var viewModel = new EditRewardPunishmentDialogContentViewModel(dialog, _rewardPunishmentManagementService, _userManagementService, rewardPunishment);
        dialog.Content = new EditRewardPunishmentDialogContent()
        {
            DataContext = viewModel
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            await LoadRewardPunishmentsAsync();
        }
    }

    private async Task ShowDetailsDialogAsync(RewardPunishment rewardPunishment)
    {
        var dialog = new ContentDialog()
        {
            Title = "奖罚记录详情",
            PrimaryButtonText = "关闭",
            CloseButtonText = "取消"
        };

        var viewModel = new ShowRewardPunishmentDialogContentViewModel(dialog, rewardPunishment);
        dialog.Content = new ShowRewardPunishmentDialogContent()
        {
            DataContext = viewModel
        };

        await dialog.ShowAsync();
    }

    private async Task SearchRewardPunishmentsAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadRewardPunishmentsAsync();
            return;
        }
        
        RewardPunishments.Clear();
        var result = await _rewardPunishmentManagementService.SearchRewardPunishmentsAsync(SearchText);
        if (result.Count == 0)
        {
            var dialog = new FluentAvalonia.UI.Controls.ContentDialog()
            {
                Title = "提示",
                Content = "没有查询到相关奖罚记录！",
                PrimaryButtonText = "好的"
            };
            await dialog.ShowAsync();
        }
        else
        {
            foreach (var rp in result)
                RewardPunishments.Add(rp);
        }
    }

    private async Task RefreshCurrentPageAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadRewardPunishmentsAsync();
        }
        else
        {
            await SearchRewardPunishmentsAsync();
        }
    }
}