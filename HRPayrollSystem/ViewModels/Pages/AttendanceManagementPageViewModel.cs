using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Platform.Storage;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.Views.Dialogs;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Pages
{
    public class AttendanceManagementPageViewModel : ViewModelBase
    {
        private readonly AttendanceManagementService _attendanceService;
        private readonly DepartmentManagementService _departmentService;
        private readonly EmployeeManagementService _employeeService;
        private readonly UserManagementService _userService;
        private readonly RewardPunishmentManagementService _rewardPunishmentManagementService;
        private bool _isLoading;

        public ICommand ShowAddAttendanceDialogCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand DeleteAttendanceCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ImportCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand ShowAttendanceDetailsCommand { get; }
        
        // Pagination Commands
        public ICommand FirstPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand LastPageCommand { get; }
        public ICommand GoToPageCommand { get; }

        private string _searchText = "";
        public string SearchText
        {
            get => _searchText;
            set => this.RaiseAndSetIfChanged(ref _searchText, value);
        }

        private DateTime _startDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
        public DateTime StartDate
        {
            get => _startDate;
            set => this.RaiseAndSetIfChanged(ref _startDate, value);
        }

        private DateTime _endDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));
        public DateTime EndDate
        {
            get => _endDate;
            set => this.RaiseAndSetIfChanged(ref _endDate, value);
        }

        private Department? _selectedDepartment;
        public Department? SelectedDepartment
        {
            get => _selectedDepartment;
            set => this.RaiseAndSetIfChanged(ref _selectedDepartment, value);
        }
        
        // Pagination Properties
        private int _currentPage = 1;
        public int CurrentPage
        {
            get => _currentPage;
            set => this.RaiseAndSetIfChanged(ref _currentPage, value);
        }

        private int _pageSize = 20;
        public int PageSize
        {
            get => _pageSize;
            set => this.RaiseAndSetIfChanged(ref _pageSize, value);
        }

        private int _totalPages = 1;
        public int TotalPages
        {
            get => _totalPages;
            set => this.RaiseAndSetIfChanged(ref _totalPages, value);
        }

        private int _totalItems = 0;
        public int TotalItems
        {
            get => _totalItems;
            set => this.RaiseAndSetIfChanged(ref _totalItems, value);
        }

        private bool _isFirstPage = true;
        public bool IsFirstPage
        {
            get => _isFirstPage;
            set => this.RaiseAndSetIfChanged(ref _isFirstPage, value);
        }

        private bool _isLastPage = true;
        public bool IsLastPage
        {
            get => _isLastPage;
            set => this.RaiseAndSetIfChanged(ref _isLastPage, value);
        }

        private string _goToPageText = "1";
        public string GoToPageText
        {
            get => _goToPageText;
            set => this.RaiseAndSetIfChanged(ref _goToPageText, value);
        }

        public ObservableCollection<Department> Departments { get; } = new();
        public ObservableCollection<AttendanceSummary> Attendances { get; } = new();
        public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };

        public AttendanceManagementPageViewModel(
            AttendanceManagementService attendanceService,
            DepartmentManagementService departmentService,
            EmployeeManagementService employeeService,
            UserManagementService userService,
            RewardPunishmentManagementService rewardPunishmentManagementService)
        {
            _attendanceService = attendanceService;
            _departmentService = departmentService;
            _employeeService = employeeService;
            _userService = userService;
            _rewardPunishmentManagementService = rewardPunishmentManagementService;

            ShowAddAttendanceDialogCommand = ReactiveCommand.CreateFromTask(ShowAddAttendanceDialogAsync);
            RefreshCommand = ReactiveCommand.CreateFromTask(LoadAttendancesAsync);
            DeleteAttendanceCommand = ReactiveCommand.CreateFromTask<Attendance>(DeleteAttendanceAsync);
            SearchCommand = ReactiveCommand.CreateFromTask(SearchAttendanceAsync);
            ImportCommand = ReactiveCommand.CreateFromTask(ImportAttendanceAsync);
            ExportCommand = ReactiveCommand.CreateFromTask(ExportAttendanceAsync);
            ShowAttendanceDetailsCommand = ReactiveCommand.CreateFromTask<AttendanceSummary>(ShowAttendanceDetailsAsync);
            
            // Initialize pagination commands
            FirstPageCommand = ReactiveCommand.CreateFromTask(GoToFirstPageAsync);
            PreviousPageCommand = ReactiveCommand.CreateFromTask(GoToPreviousPageAsync);
            NextPageCommand = ReactiveCommand.CreateFromTask(GoToNextPageAsync);
            LastPageCommand = ReactiveCommand.CreateFromTask(GoToLastPageAsync);
            GoToPageCommand = ReactiveCommand.CreateFromTask(GoToSpecificPageAsync);
            
            // Subscribe to page size changes
            this.WhenAnyValue(x => x.PageSize)
                .Subscribe(async _ => await OnPageSizeChangedAsync());

            // 初始化
            _ = InitializeAsync();
        }

        private async Task InitializeAsync()
        {
            await LoadDepartmentsAsync();
            await LoadAttendancesAsync();
        }

        private async Task LoadDepartmentsAsync()
        {
            var departments = await _departmentService.FindAllAsync();
            Departments.Clear();
            Departments.Add(new Department { Id = 0, Name = "全部" }); // 添加一个全部选项
            foreach (var dept in departments)
            {
                Departments.Add(dept);
            }
        }

        private async Task LoadAttendancesAsync()
        {
            if (_isLoading) return;
            _isLoading = true;

            try
            {
                var departmentId = SelectedDepartment?.Id > 0 ? SelectedDepartment?.Id : null;
                var summaries = await _attendanceService.FindByDateRangeAsync(StartDate, EndDate, departmentId);
                
                // 如果有搜索文本，进行过滤
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    var searchTermLower = SearchText.ToLowerInvariant();
                    summaries = summaries.Where(a => 
                        (a.EmployeeName?.ToLowerInvariant().Contains(searchTermLower) ?? false) ||
                        (a.EmployeeNumber?.ToLowerInvariant().Contains(searchTermLower) ?? false) ||
                        (a.Department?.ToLowerInvariant().Contains(searchTermLower) ?? false) ||
                        (a.Position?.ToLowerInvariant().Contains(searchTermLower) ?? false)
                    ).ToList();
                }
                
                // Update pagination information
                TotalItems = summaries.Count;
                TotalPages = (int)Math.Ceiling((double)TotalItems / PageSize);
                if (CurrentPage < 1) CurrentPage = 1;
                if (CurrentPage > TotalPages && TotalPages > 0) CurrentPage = TotalPages;
                
                // Apply pagination
                var paged = summaries.Skip((CurrentPage - 1) * PageSize).Take(PageSize);
                
                Attendances.Clear();
                foreach (var summary in paged)
                {
                    Attendances.Add(summary);
                }
                
                // Update pagination state
                IsFirstPage = CurrentPage == 1;
                IsLastPage = CurrentPage == TotalPages || TotalPages == 0;
                GoToPageText = CurrentPage.ToString();
            }
            finally
            {
                _isLoading = false;
            }
        }
        
        private async Task OnPageSizeChangedAsync()
        {
            CurrentPage = 1;
            await LoadAttendancesAsync();
        }
        
        private async Task GoToFirstPageAsync()
        {
            if (!IsFirstPage)
            {
                CurrentPage = 1;
                await LoadAttendancesAsync();
            }
        }

        private async Task GoToPreviousPageAsync()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                await LoadAttendancesAsync();
            }
        }

        private async Task GoToNextPageAsync()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                await LoadAttendancesAsync();
            }
        }

        private async Task GoToLastPageAsync()
        {
            if (!IsLastPage && TotalPages > 0)
            {
                CurrentPage = TotalPages;
                await LoadAttendancesAsync();
            }
        }

        private async Task GoToSpecificPageAsync()
        {
            if (int.TryParse(GoToPageText, out int targetPage))
            {
                if (targetPage >= 1 && targetPage <= TotalPages && targetPage != CurrentPage)
                {
                    CurrentPage = targetPage;
                    await LoadAttendancesAsync();
                    return;
                }
            }
            
            // Reset to current page if invalid input
            GoToPageText = CurrentPage.ToString();
        }

        private async Task DeleteAttendanceAsync(Attendance attendance)
        {
            var dialog = new ContentDialog
            {
                Title = "确认操作",
                Content = "确定要删除该考勤记录吗？",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消"
            };

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                var deleted = await _attendanceService.DeleteAttendanceAsync(attendance.Id);
                if (deleted)
                {
                    await LoadAttendancesAsync();
                }
                else
                {
                    var errorDialog = new ContentDialog
                    {
                        Title = "错误",
                        Content = "删除考勤记录失败！",
                        PrimaryButtonText = "确定"
                    };
                    await errorDialog.ShowAsync();
                }
            }
        }

        private async Task SearchAttendanceAsync()
        {
            CurrentPage = 1;
            await LoadAttendancesAsync();
        }

        private async Task ShowAddAttendanceDialogAsync()
        {
            var contentDialog = new ContentDialog
            {
                Title = "添加考勤记录",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
            };
            
            var viewModel = new AddAttendanceDialogContentViewModel(contentDialog, _attendanceService, _userService, _rewardPunishmentManagementService);
            contentDialog.Content = new AddAttendanceDialogContent()
            {
                DataContext = viewModel
            };

            var result = await contentDialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                await LoadAttendancesAsync();
            }
        }
        
        private async Task ShowAttendanceDetailsAsync(AttendanceSummary summary)
        {
            if (summary == null) return;
            
            // 获取员工的考勤详情
            var attendances = await _attendanceService.FindByEmployeeAndMonthAsync(
                await GetEmployeeIdByEmployeeNumberAsync(summary.EmployeeNumber),
                StartDate.Year,
                StartDate.Month);
            
            // 创建一个详情内容
            var content = new TextBlock
            {
                Text = $"员工：{summary.EmployeeName} ({summary.EmployeeNumber})\n" +
                      $"部门：{summary.Department}\n" +
                      $"职位：{summary.Position}\n\n" +
                      $"考勤明细（{StartDate:yyyy年MM月}）：\n" +
                      $"正常出勤：{summary.NormalCount} 次\n" +
                      $"迟到：{summary.LateCount} 次\n" +
                      $"早退：{summary.EarlyLeaveCount} 次\n" +
                      $"旷工：{summary.AbsentCount} 次\n" +
                      $"请假：{summary.LeaveCount} 次\n",
                TextWrapping = Avalonia.Media.TextWrapping.Wrap
            };
            
            var dialog = new ContentDialog
            {
                Title = "考勤详情",
                Content = content,
                PrimaryButtonText = "确定"
            };
            
            await dialog.ShowAsync();
        }
        
        private async Task<int> GetEmployeeIdByEmployeeNumberAsync(string employeeNumber)
        {
            var employee = await _employeeService.FindByEmployeeNumberAsync(employeeNumber);
            return employee?.Id ?? 0;
        }

        private async Task ImportAttendanceAsync()
        {
            try
            {
                // 选择Excel文件
                var lifetime = Avalonia.Application.Current?.ApplicationLifetime as Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime;
                var topLevel = TopLevel.GetTopLevel(lifetime?.MainWindow);
                var filePickerOptions = new FilePickerOpenOptions
                {
                    Title = "选择考勤数据Excel文件",
                    AllowMultiple = false,
                    FileTypeFilter = new[] 
                    { 
                        new FilePickerFileType("Excel文件") { Patterns = new[] { "*.xlsx", "*.xls" } }
                    }
                };
                
                var result = await topLevel.StorageProvider.OpenFilePickerAsync(filePickerOptions);
                if (result == null || !result.Any())
                {
                    return;
                }

                var file = result.First();
                var filePath = file.TryGetLocalPath();
                
                if (string.IsNullOrEmpty(filePath))
                {
                    var dialog = new ContentDialog
                    {
                        Title = "错误",
                        Content = "无法获取文件路径。",
                        PrimaryButtonText = "确定"
                    };
                    await dialog.ShowAsync();
                    return;
                }
                
                // 显示进度对话框
                var progressDialog = new ContentDialog
                {
                    Title = "正在导入",
                    Content = "正在导入考勤数据，请稍候...",
                    CloseButtonText = "取消"
                };
                
                // 在后台任务中导入数据
                var importTask = Task.Run(async () => 
                {
                    try 
                    {
                        return await _attendanceService.ImportAttendanceFromExcelAsync(filePath);
                    }
                    catch (Exception ex)
                    {
                        return (0, 0, new List<string> { $"导入过程中发生错误: {ex.Message}" });
                    }
                });
                
                var dialogTask = progressDialog.ShowAsync();
                var completedTask = await Task.WhenAny(importTask, dialogTask);
                
                if (completedTask == dialogTask)
                {
                    // 用户取消了导入
                    return;
                }
                
                // 导入完成，关闭进度对话框
                progressDialog.Hide();
                
                // 获取导入结果
                var (success, failed, errors) = await importTask;
                
                // 显示导入结果
                var resultContent = $"导入完成！\n成功导入: {success} 条记录\n导入失败: {failed} 条记录";
                
                if (errors.Any())
                {
                    resultContent += "\n\n错误信息:";
                    foreach (var error in errors.Take(10))
                    {
                        resultContent += $"\n- {error}";
                    }
                    
                    if (errors.Count > 10)
                    {
                        resultContent += $"\n... 以及其他 {errors.Count - 10} 个错误";
                    }
                }
                
                var resultDialog = new ContentDialog
                {
                    Title = "导入结果",
                    Content = resultContent,
                    PrimaryButtonText = "确定"
                };
                
                await resultDialog.ShowAsync();
                
                // 刷新数据
                await LoadAttendancesAsync();
            }
            catch (Exception ex)
            {
                var dialog = new ContentDialog
                {
                    Title = "操作失败",
                    Content = $"导入考勤数据时发生错误: {ex.Message}",
                    PrimaryButtonText = "确定"
                };
                await dialog.ShowAsync();
            }
        }

        private async Task ExportAttendanceAsync()
        {
            try
            {
                // 导出Excel文件
                var filePath = await _attendanceService.ExportAttendanceToExcelAsync(StartDate, EndDate, 
                    SelectedDepartment?.Id > 0 ? SelectedDepartment?.Id : null);
                
                if (string.IsNullOrEmpty(filePath))
                {
                    var dialog = new ContentDialog
                    {
                        Title = "操作失败",
                        Content = "导出考勤数据失败，请重试。",
                        PrimaryButtonText = "确定"
                    };
                    await dialog.ShowAsync();
                    return;
                }
                
                // 显示导出成功对话框
                var resultDialog = new ContentDialog
                {
                    Title = "导出成功",
                    Content = $"考勤数据已成功导出到:\n{filePath}\n\n是否立即打开该文件？",
                    PrimaryButtonText = "打开文件",
                    SecondaryButtonText = "打开所在文件夹",
                    CloseButtonText = "关闭"
                };
                
                var result = await resultDialog.ShowAsync();
                
                if (result == ContentDialogResult.Primary)
                {
                    // 打开文件
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else if (result == ContentDialogResult.Secondary)
                {
                    // 打开所在文件夹
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = Path.GetDirectoryName(filePath),
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                var dialog = new ContentDialog
                {
                    Title = "操作失败",
                    Content = $"导出考勤数据时发生错误: {ex.Message}",
                    PrimaryButtonText = "确定"
                };
                await dialog.ShowAsync();
            }
        }
    }
} 
