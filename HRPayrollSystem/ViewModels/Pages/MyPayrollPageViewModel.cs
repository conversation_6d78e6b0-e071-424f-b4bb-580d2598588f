using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Pages
{
    public class MyPayrollPageViewModel : ViewModelBase
    {
        private readonly PayrollManagementService _payrollManagementService;
        private readonly EmployeeManagementService _employeeManagementService;
        private bool _isLoading;
        private Employee? _currentEmployee;

        public ICommand RefreshCommand { get; }
        public ICommand ViewDetailsCommand { get; }
        
        // 分页相关命令
        public ICommand FirstPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand LastPageCommand { get; }
        public ICommand GoToPageCommand { get; }

        // 年份和月份选择
        public ObservableCollection<int> Years { get; } = new();
        public ObservableCollection<int> Months { get; } = new();
        
        private int _startYear;
        public int StartYear
        {
            get => _startYear;
            set => this.RaiseAndSetIfChanged(ref _startYear, value);
        }

        private int _startMonth;
        public int StartMonth
        {
            get => _startMonth;
            set => this.RaiseAndSetIfChanged(ref _startMonth, value);
        }

        private int _endYear;
        public int EndYear
        {
            get => _endYear;
            set => this.RaiseAndSetIfChanged(ref _endYear, value);
        }

        private int _endMonth;
        public int EndMonth
        {
            get => _endMonth;
            set => this.RaiseAndSetIfChanged(ref _endMonth, value);
        }

        // 分页属性
        private int _currentPage = 1;
        public int CurrentPage
        {
            get => _currentPage;
            set => this.RaiseAndSetIfChanged(ref _currentPage, value);
        }

        private int _pageSize = 20;
        public int PageSize
        {
            get => _pageSize;
            set => this.RaiseAndSetIfChanged(ref _pageSize, value);
        }

        private int _totalPages = 1;
        public int TotalPages
        {
            get => _totalPages;
            set => this.RaiseAndSetIfChanged(ref _totalPages, value);
        }

        private int _totalItems = 0;
        public int TotalItems
        {
            get => _totalItems;
            set => this.RaiseAndSetIfChanged(ref _totalItems, value);
        }

        private bool _isFirstPage = true;
        public bool IsFirstPage
        {
            get => _isFirstPage;
            set => this.RaiseAndSetIfChanged(ref _isFirstPage, value);
        }

        private bool _isLastPage = true;
        public bool IsLastPage
        {
            get => _isLastPage;
            set => this.RaiseAndSetIfChanged(ref _isLastPage, value);
        }

        private string _goToPageText = "1";
        public string GoToPageText
        {
            get => _goToPageText;
            set => this.RaiseAndSetIfChanged(ref _goToPageText, value);
        }

        public ObservableCollection<Payroll> MyPayrolls { get; } = new();
        public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };

        public MyPayrollPageViewModel(
            PayrollManagementService payrollManagementService,
            EmployeeManagementService employeeManagementService)
        {
            _payrollManagementService = payrollManagementService;
            _employeeManagementService = employeeManagementService;
            
            // 初始化年份列表 (当前年份及前5年)
            for (int i = 0; i >= -5; i--)
            {
                Years.Add(DateTime.Now.Year + i);
            }
            
            // 初始化月份列表 (1-12月)
            for (int i = 1; i <= 12; i++)
            {
                Months.Add(i);
            }
            
            // 设置默认时间范围（去年1月到当前月份）
            StartYear = DateTime.Now.Year - 1;
            StartMonth = 1;
            EndYear = DateTime.Now.Year;
            EndMonth = DateTime.Now.Month;

            // 初始化命令
            RefreshCommand = ReactiveCommand.CreateFromTask(LoadMyPayrollsAsync);
            ViewDetailsCommand = ReactiveCommand.CreateFromTask<Payroll>(ViewPayrollDetailsAsync);
            
            // 初始化分页命令
            FirstPageCommand = ReactiveCommand.CreateFromTask(GoToFirstPageAsync);
            PreviousPageCommand = ReactiveCommand.CreateFromTask(GoToPreviousPageAsync);
            NextPageCommand = ReactiveCommand.CreateFromTask(GoToNextPageAsync);
            LastPageCommand = ReactiveCommand.CreateFromTask(GoToLastPageAsync);
            GoToPageCommand = ReactiveCommand.CreateFromTask(GoToSpecificPageAsync);
            
            // 监听页面大小变化
            this.WhenAnyValue(x => x.PageSize)
                .Subscribe(async _ => await OnPageSizeChangedAsync());
                
            // 监听时间范围变化
            this.WhenAnyValue(x => x.StartYear, x => x.StartMonth, x => x.EndYear, x => x.EndMonth)
                .Subscribe(async _ => await OnDateRangeChangedAsync());
            
            // 初始化
            _ = InitializeAsync();
        }

        private async Task InitializeAsync()
        {
            try
            {
                // 获取当前登录用户的员工信息
                var currentUser = CurrentUserService.CurrentUser;
                if (currentUser?.EmployeeNumber != null)
                {
                    _currentEmployee = await _employeeManagementService.FindByEmployeeNumberAsync(currentUser.EmployeeNumber);
                }
                
                if (_currentEmployee == null)
                {
                    var dialog = new ContentDialog
                    {
                        Title = "错误",
                        Content = "无法获取当前用户的员工信息，请联系管理员。",
                        PrimaryButtonText = "确定"
                    };
                    await dialog.ShowAsync();
                    return;
                }
                
                // 加载工资单数据
                await LoadMyPayrollsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化我的工资页面时发生错误: {ex.Message}");
            }
        }

        private async Task LoadMyPayrollsAsync()
        {
            if (_isLoading || _currentEmployee == null) return;
            _isLoading = true;

            MyPayrolls.Clear();
            
            try
            {
                // 构建月份范围字符串
                string startMonthStr = $"{StartYear}-{StartMonth:D2}";
                string endMonthStr = $"{EndYear}-{EndMonth:D2}";
                
                // 获取工资单数据
                var payrolls = await _payrollManagementService.GetPayrollsByEmployeeIdAsync(
                    _currentEmployee.Id, startMonthStr, endMonthStr, CurrentPage, PageSize);
                
                // 获取总数量
                var totalCount = await _payrollManagementService.CountPayrollsByEmployeeIdAsync(
                    _currentEmployee.Id, startMonthStr, endMonthStr);
                
                // 更新分页信息
                TotalItems = totalCount;
                TotalPages = (int)Math.Ceiling((double)totalCount / PageSize);
                UpdatePaginationState();
                
                // 更新UI
                foreach (var payroll in payrolls)
                {
                    MyPayrolls.Add(payroll);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载我的工资单时发生错误: {ex.Message}");
                
                var dialog = new ContentDialog
                {
                    Title = "错误",
                    Content = "加载工资单数据失败，请稍后重试。",
                    PrimaryButtonText = "确定"
                };
                await dialog.ShowAsync();
            }
            finally
            {
                _isLoading = false;
            }
        }

        private void UpdatePaginationState()
        {
            IsFirstPage = CurrentPage == 1;
            IsLastPage = CurrentPage == TotalPages || TotalPages == 0;
            GoToPageText = CurrentPage.ToString();
        }

        private async Task OnPageSizeChangedAsync()
        {
            CurrentPage = 1;
            await LoadMyPayrollsAsync();
        }

        private async Task OnDateRangeChangedAsync()
        {
            CurrentPage = 1;
            await LoadMyPayrollsAsync();
        }

        private async Task GoToFirstPageAsync()
        {
            if (!IsFirstPage)
            {
                CurrentPage = 1;
                await LoadMyPayrollsAsync();
            }
        }

        private async Task GoToPreviousPageAsync()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                await LoadMyPayrollsAsync();
            }
        }

        private async Task GoToNextPageAsync()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                await LoadMyPayrollsAsync();
            }
        }

        private async Task GoToLastPageAsync()
        {
            if (!IsLastPage && TotalPages > 0)
            {
                CurrentPage = TotalPages;
                await LoadMyPayrollsAsync();
            }
        }

        private async Task GoToSpecificPageAsync()
        {
            if (int.TryParse(GoToPageText, out int targetPage))
            {
                if (targetPage >= 1 && targetPage <= TotalPages && targetPage != CurrentPage)
                {
                    CurrentPage = targetPage;
                    await LoadMyPayrollsAsync();
                    return;
                }
                if (targetPage == CurrentPage)
                {
                    return;
                }
            }
            
            // 重置为当前页面
            GoToPageText = CurrentPage.ToString();
            
            var dialog = new ContentDialog
            {
                Title = "错误",
                Content = "请输入有效的页码！",
                PrimaryButtonText = "确定"
            };
            await dialog.ShowAsync();
        }

        private async Task ViewPayrollDetailsAsync(Payroll payroll)
        {
            try
            {
                var dialog = new ContentDialog
                {
                    Title = $"{payroll.PayMonth} 工资单详情",
                    Content = $"员工：{payroll.EmployeeName} ({payroll.EmployeeNumber})\n" +
                             $"部门：{payroll.Department}\n" +
                             $"职位：{payroll.Position}\n" +
                             $"基本工资：¥{payroll.BaseSalary:F2}\n" +
                             $"奖励金额：¥{payroll.RewardAmount:F2}\n" +
                             $"处罚金额：¥{payroll.PenaltyAmount:F2}\n" +
                             $"考勤扣款：¥{payroll.AttendanceDeduction:F2}\n" +
                             $"实发工资：¥{payroll.NetSalary:F2}\n" +
                             $"状态：{payroll.Status}",
                    PrimaryButtonText = "确定"
                };
                
                await dialog.ShowAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查看工资单详情时发生错误: {ex.Message}");
            }
        }
    }
}