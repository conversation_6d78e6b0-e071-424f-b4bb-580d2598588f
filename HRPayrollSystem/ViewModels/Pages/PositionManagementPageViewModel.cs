using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.Views.Dialogs;
using System.Linq;

namespace HRPayrollSystem.ViewModels.Pages;

public partial class PositionManagementPageViewModel : ViewModelBase
{
    private readonly PositionManagementService _positionManagementService;
    private bool _isLoading;
    private string _searchText = "";

    public ICommand ShowAddPositionDialogCommand { get; }
    public ICommand RefreshCommand { get; }
    public ICommand DeletePositionCommand { get; }
    public ICommand EditPositionCommand { get; }
    public ICommand SearchCommand { get; }
    public ICommand FirstPageCommand { get; }
    public ICommand PreviousPageCommand { get; }
    public ICommand NextPageCommand { get; }
    public ICommand LastPageCommand { get; }
    public ICommand GoToPageCommand { get; }

    private int _currentPage = 1;
    public int CurrentPage
    {
        get => _currentPage;
        set => this.RaiseAndSetIfChanged(ref _currentPage, value);
    }

    private int _pageSize = 20;
    public int PageSize
    {
        get => _pageSize;
        set => this.RaiseAndSetIfChanged(ref _pageSize, value);
    }

    private int _totalPages = 1;
    public int TotalPages
    {
        get => _totalPages;
        set => this.RaiseAndSetIfChanged(ref _totalPages, value);
    }

    private int _totalItems = 0;
    public int TotalItems
    {
        get => _totalItems;
        set => this.RaiseAndSetIfChanged(ref _totalItems, value);
    }

    private bool _isFirstPage = true;
    public bool IsFirstPage
    {
        get => _isFirstPage;
        set => this.RaiseAndSetIfChanged(ref _isFirstPage, value);
    }

    private bool _isLastPage = true;
    public bool IsLastPage
    {
        get => _isLastPage;
        set => this.RaiseAndSetIfChanged(ref _isLastPage, value);
    }

    private string _pageSizeText = "20";
    public string PageSizeText
    {
        get => _pageSizeText;
        set => this.RaiseAndSetIfChanged(ref _pageSizeText, value);
    }

    private string _goToPageText = "1";
    public string GoToPageText
    {
        get => _goToPageText;
        set => this.RaiseAndSetIfChanged(ref _goToPageText, value);
    }

    public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };

    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }

    public ObservableCollection<Position> Positions { get; } = new();

    public PositionManagementPageViewModel(PositionManagementService positionManagementService)
    {
        _positionManagementService = positionManagementService;
        ShowAddPositionDialogCommand = ReactiveCommand.CreateFromTask(ShowAddPositionDialogAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(LoadPositionsAsync);
        DeletePositionCommand = ReactiveCommand.CreateFromTask<Position>(DeleteByIdAsync);
        EditPositionCommand = ReactiveCommand.CreateFromTask<Position>(ShowEditPositionDialogAsync);
        SearchCommand = ReactiveCommand.CreateFromTask(SearchPositionsAsync);
        FirstPageCommand = ReactiveCommand.CreateFromTask(GoToFirstPageAsync);
        PreviousPageCommand = ReactiveCommand.CreateFromTask(GoToPreviousPageAsync);
        NextPageCommand = ReactiveCommand.CreateFromTask(GoToNextPageAsync);
        LastPageCommand = ReactiveCommand.CreateFromTask(GoToLastPageAsync);
        GoToPageCommand = ReactiveCommand.CreateFromTask(GoToSpecificPageAsync);

        this.WhenAnyValue(x => x.PageSize)
            .Subscribe(async _ => await OnPageSizeChangedAsync());

        _ = LoadPositionsAsync();
    }

    private async Task DeleteByIdAsync(Position position)
    {
        var deleted = await _positionManagementService.DeletePositionAsync(position.Id);
        if (deleted)
        {
            Console.WriteLine(position.ToString());
            Positions.Remove(position);
        }
    }

    private async Task ShowAddPositionDialogAsync()
    {
        var dialog = new ContentDialog()
        {
            Title = "添加岗位",
            PrimaryButtonText = "添加",
            SecondaryButtonText = "清空",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new AddPositionDialogContentViewModel(dialog, _positionManagementService);
        dialog.Content = new AddPositionDialogContent()
        {
            DataContext = viewModel
        };

        await dialog.ShowAsync();
        await LoadPositionsAsync();
    }

    private async Task LoadPositionsAsync()
    {
        if (_isLoading) return;
        _isLoading = true;

        Positions.Clear();
        var positionsFromDb = await _positionManagementService.FindAllAsync();
        TotalItems = positionsFromDb.Count;
        TotalPages = (int)Math.Ceiling((double)TotalItems / PageSize);
        if (CurrentPage < 1) CurrentPage = 1;
        if (CurrentPage > TotalPages) CurrentPage = TotalPages;
        var paged = positionsFromDb.Skip((CurrentPage - 1) * PageSize).Take(PageSize);
        foreach (var position in paged)
        {
            Positions.Add(position);
        }
        IsFirstPage = CurrentPage == 1;
        IsLastPage = CurrentPage == TotalPages || TotalPages == 0;
        GoToPageText = CurrentPage.ToString();
        _isLoading = false;
    }

    private async Task OnPageSizeChangedAsync()
    {
        CurrentPage = 1;
        await LoadPositionsAsync();
    }

    private async Task GoToFirstPageAsync()
    {
        if (!IsFirstPage)
        {
            CurrentPage = 1;
            await LoadPositionsAsync();
        }
    }

    private async Task GoToPreviousPageAsync()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            await LoadPositionsAsync();
        }
    }

    private async Task GoToNextPageAsync()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            await LoadPositionsAsync();
        }
    }

    private async Task GoToLastPageAsync()
    {
        if (!IsLastPage)
        {
            CurrentPage = TotalPages;
            await LoadPositionsAsync();
        }
    }

    private async Task GoToSpecificPageAsync()
    {
        if (int.TryParse(GoToPageText, out int targetPage))
        {
            if (targetPage >= 1 && targetPage <= TotalPages && targetPage != CurrentPage)
            {
                CurrentPage = targetPage;
                await RefreshCurrentPageAsync();
                return;
            }
            if (targetPage == CurrentPage)
            {
                return;
            }
        }
        GoToPageText = CurrentPage.ToString();
        var dialog = new ContentDialog()
        {
            Title = "错误",
            Content = "请输入有效的页码！",
            PrimaryButtonText = "好的",
            DefaultButton = ContentDialogButton.Primary
        };
        await dialog.ShowAsync();
    }

    private async Task ShowEditPositionDialogAsync(Position position)
    {
        var dialog = new ContentDialog()
        {
            Title = "编辑岗位",
            PrimaryButtonText = "保存",
            SecondaryButtonText = "重置",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new EditPositionDialogContentViewModel(dialog, _positionManagementService, position);
        dialog.Content = new EditPositionDialogContent()
        {
            DataContext = viewModel
        };

        await dialog.ShowAsync();
        await LoadPositionsAsync();
    }

    private async Task SearchPositionsAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadPositionsAsync();
            return;
        }
        Positions.Clear();
        var result = await _positionManagementService.SearchPositionsAsync(SearchText);
        if (result.Count == 0)
        {
            var dialog = new FluentAvalonia.UI.Controls.ContentDialog()
            {
                Title = "提示",
                Content = "没有查询到相关岗位！",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            await dialog.ShowAsync();
        }
        else
        {
            foreach (var p in result)
                Positions.Add(p);
        }
    }

    private async Task RefreshCurrentPageAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadPositionsAsync();
        }
        else
        {
            await SearchPositionsAsync();
        }
    }
}