﻿using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.Views.Dialogs;
using System.Linq;

namespace HRPayrollSystem.ViewModels.Pages;

public partial class EmployeeManagementPageViewModel : ViewModelBase
{
    private readonly EmployeeManagementService _employeeManagementService;
    private readonly DepartmentManagementService _departmentManagementService;
    private readonly PositionManagementService _positionManagementService;
    private readonly TransferService _transferService;
    private bool _isLoading;

    public ICommand ShowAddEmployeeDialogCommand { get; }
    public ICommand RefreshCommand { get; }
    public ICommand DeleteEmployeeCommand { get; }
    public ICommand EditEmployeeCommand { get; }
    public ICommand TransferEmployeeCommand { get; }
    public ICommand ShowEmployeeDetailsCommand { get; }
    
    public ICommand SearchCommand { get; }
    
    // Pagination Commands
    public ICommand FirstPageCommand { get; }
    public ICommand PreviousPageCommand { get; }
    public ICommand NextPageCommand { get; }
    public ICommand LastPageCommand { get; }
    public ICommand GoToPageCommand { get; }

    private string _searchText = "";
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }
    
    // Pagination Properties
    private int _currentPage = 1;
    public int CurrentPage
    {
        get => _currentPage;
        set => this.RaiseAndSetIfChanged(ref _currentPage, value);
    }

    private int _pageSize = 20;
    public int PageSize
    {
        get => _pageSize;
        set => this.RaiseAndSetIfChanged(ref _pageSize, value);
    }

    private int _totalPages = 1;
    public int TotalPages
    {
        get => _totalPages;
        set => this.RaiseAndSetIfChanged(ref _totalPages, value);
    }

    private int _totalItems = 0;
    public int TotalItems
    {
        get => _totalItems;
        set => this.RaiseAndSetIfChanged(ref _totalItems, value);
    }

    private bool _isFirstPage = true;
    public bool IsFirstPage
    {
        get => _isFirstPage;
        set => this.RaiseAndSetIfChanged(ref _isFirstPage, value);
    }

    private bool _isLastPage = true;
    public bool IsLastPage
    {
        get => _isLastPage;
        set => this.RaiseAndSetIfChanged(ref _isLastPage, value);
    }

    private string _goToPageText = "1";
    public string GoToPageText
    {
        get => _goToPageText;
        set => this.RaiseAndSetIfChanged(ref _goToPageText, value);
    }
    
    public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };

    public ObservableCollection<Employee> Employees { get; } = new();

    public EmployeeManagementPageViewModel(
        EmployeeManagementService employeeManagementService,
        DepartmentManagementService departmentManagementService,
        PositionManagementService positionManagementService,
        TransferService transferService)
    {
        _employeeManagementService = employeeManagementService;
        _departmentManagementService = departmentManagementService;
        _positionManagementService = positionManagementService;
        _transferService = transferService;

        ShowAddEmployeeDialogCommand = ReactiveCommand.CreateFromTask(ShowAddEmployeeDialogAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(LoadEmployeesAsync);
        DeleteEmployeeCommand = ReactiveCommand.CreateFromTask<Employee>(DeleteByIdAsync);
        EditEmployeeCommand = ReactiveCommand.CreateFromTask<Employee>(ShowEditEmployeeDialogAsync);
        TransferEmployeeCommand = ReactiveCommand.CreateFromTask<Employee>(ShowTransferEmployeeDialogAsync);
        ShowEmployeeDetailsCommand = ReactiveCommand.CreateFromTask<Employee>(ShowEmployeeDetailsDialogAsync);
        SearchCommand = ReactiveCommand.CreateFromTask(SearchEmployeesAsync);
        
        // Initialize pagination commands
        FirstPageCommand = ReactiveCommand.CreateFromTask(GoToFirstPageAsync);
        PreviousPageCommand = ReactiveCommand.CreateFromTask(GoToPreviousPageAsync);
        NextPageCommand = ReactiveCommand.CreateFromTask(GoToNextPageAsync);
        LastPageCommand = ReactiveCommand.CreateFromTask(GoToLastPageAsync);
        GoToPageCommand = ReactiveCommand.CreateFromTask(GoToSpecificPageAsync);
        
        // Subscribe to page size changes
        this.WhenAnyValue(x => x.PageSize)
            .Subscribe(async _ => await OnPageSizeChangedAsync());

        _ = InitializeAndLoadAsync();
    }

    private async Task InitializeAndLoadAsync()
    {
        await _employeeManagementService.InitializeBasicDataAsync();
        await LoadEmployeesAsync();
    }

    private async Task DeleteByIdAsync(Employee employee)
    {
        var deleted = await _employeeManagementService.DeleteEmployeeAsync(employee.Id);
        if (deleted)
        {
            Console.WriteLine(employee.ToString());
            Employees.Remove(employee);
        }
    }

    private async Task ShowAddEmployeeDialogAsync()
    {
        var dialog = new ContentDialog()
        {
            Title = "添加员工",
            PrimaryButtonText = "添加",
            SecondaryButtonText = "清空",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new AddEmployeeDialogContentViewModel(dialog, _employeeManagementService);
        dialog.Content = new AddEmployeeDialogContent()
        {
            DataContext = viewModel
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            await LoadEmployeesAsync();
        }
    }

    private async Task LoadEmployeesAsync()
    {
        if (_isLoading) return;
        _isLoading = true;

        Employees.Clear();
        var employeesFromDb = await _employeeManagementService.FindAllAsync();
        
        // Update pagination information
        TotalItems = employeesFromDb.Count;
        TotalPages = (int)Math.Ceiling((double)TotalItems / PageSize);
        if (CurrentPage < 1) CurrentPage = 1;
        if (CurrentPage > TotalPages && TotalPages > 0) CurrentPage = TotalPages;
        
        // Apply pagination
        var paged = employeesFromDb.Skip((CurrentPage - 1) * PageSize).Take(PageSize);
        foreach (var employee in paged)
        {
            Employees.Add(employee);
        }
        
        // Update pagination state
        IsFirstPage = CurrentPage == 1;
        IsLastPage = CurrentPage == TotalPages || TotalPages == 0;
        GoToPageText = CurrentPage.ToString();

        _isLoading = false;
    }
    
    private async Task OnPageSizeChangedAsync()
    {
        CurrentPage = 1;
        await LoadEmployeesAsync();
    }
    
    private async Task GoToFirstPageAsync()
    {
        if (!IsFirstPage)
        {
            CurrentPage = 1;
            await LoadEmployeesAsync();
        }
    }

    private async Task GoToPreviousPageAsync()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            await LoadEmployeesAsync();
        }
    }

    private async Task GoToNextPageAsync()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            await LoadEmployeesAsync();
        }
    }

    private async Task GoToLastPageAsync()
    {
        if (!IsLastPage && TotalPages > 0)
        {
            CurrentPage = TotalPages;
            await LoadEmployeesAsync();
        }
    }

    private async Task GoToSpecificPageAsync()
    {
        if (int.TryParse(GoToPageText, out int targetPage))
        {
            if (targetPage >= 1 && targetPage <= TotalPages && targetPage != CurrentPage)
            {
                CurrentPage = targetPage;
                await LoadEmployeesAsync();
                return;
            }
        }
        
        // Reset to current page if invalid input
        GoToPageText = CurrentPage.ToString();
    }
    
    private async Task ShowEditEmployeeDialogAsync(Employee employee)
    {
        var dialog = new ContentDialog()
        {
            Title = "编辑员工",
            PrimaryButtonText = "保存",
            SecondaryButtonText = "重置",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new EditEmployeeDialogContentViewModel(dialog, _employeeManagementService, employee);
        dialog.Content = new EditEmployeeDialogContent()
        {
            DataContext = viewModel
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            await LoadEmployeesAsync();
        }
    }

    private async Task ShowTransferEmployeeDialogAsync(Employee employee)
    {
        var dialog = new ContentDialog()
        {
            Title = "员工调动",
            PrimaryButtonText = "保存",
            SecondaryButtonText = "重置",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new TransferEmployeeDialogContentViewModel(dialog, _transferService, _departmentManagementService, _positionManagementService, employee);
        dialog.Content = new TransferEmployeeDialogContent()
        {
            DataContext = viewModel
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            await LoadEmployeesAsync();
        }
    }

    // 显示员工详情对话框
    private async Task ShowEmployeeDetailsDialogAsync(Employee employee)
    {
        var dialog = new ContentDialog
        {
            Title = "员工详细信息",
            PrimaryButtonText = "关闭",
            DefaultButton = ContentDialogButton.Primary
        };

        dialog.Content = new ShowEmployeeDialogContent
        {
            DataContext = new ShowEmployeeDialogContentViewModel(dialog, employee)
        };

        await dialog.ShowAsync();
    }

    private async Task SearchEmployeesAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadEmployeesAsync();
            return;
        }
        Employees.Clear();
        var result = await _employeeManagementService.SearchEmployeesAsync(SearchText);
        if (result.Count == 0)
        {
            var dialog = new FluentAvalonia.UI.Controls.ContentDialog()
            {
                Title = "提示",
                Content = "没有查询到相关员工！",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            await dialog.ShowAsync();
        }
        else
        {
            foreach (var d in result)
                Employees.Add(d);
        }
    }
}
