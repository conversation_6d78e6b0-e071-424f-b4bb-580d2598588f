using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;
using HRPayrollSystem.ViewModels.Dialogs;
using HRPayrollSystem.Views.Dialogs;

namespace HRPayrollSystem.ViewModels.Pages;

public partial class UserManagementPageViewModel : ViewModelBase
{
    private readonly UserManagementService _userManagementService;
    private bool _isLoading;

    public ICommand ShowAddUserDialogCommand { get; }
    public ICommand RefreshCommand { get; }
    public ICommand DeleteUserCommand { get; }
    public ICommand EditUserCommand { get; }
    public ICommand SearchCommand { get; }
    public ICommand FirstPageCommand { get; }
    public ICommand PreviousPageCommand { get; }
    public ICommand NextPageCommand { get; }
    public ICommand LastPageCommand { get; }
    public ICommand GoToPageCommand { get; }

    private string _searchText = "";
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }

    private int _currentPage = 1;
    public int CurrentPage
    {
        get => _currentPage;
        set => this.RaiseAndSetIfChanged(ref _currentPage, value);
    }

    private int _pageSize = 20;
    public int PageSize
    {
        get => _pageSize;
        set => this.RaiseAndSetIfChanged(ref _pageSize, value);
    }

    private int _totalPages = 1;
    public int TotalPages
    {
        get => _totalPages;
        set => this.RaiseAndSetIfChanged(ref _totalPages, value);
    }

    private int _totalItems = 0;
    public int TotalItems
    {
        get => _totalItems;
        set => this.RaiseAndSetIfChanged(ref _totalItems, value);
    }

    private bool _isFirstPage = true;
    public bool IsFirstPage
    {
        get => _isFirstPage;
        set => this.RaiseAndSetIfChanged(ref _isFirstPage, value);
    }

    private bool _isLastPage = true;
    public bool IsLastPage
    {
        get => _isLastPage;
        set => this.RaiseAndSetIfChanged(ref _isLastPage, value);
    }

    private string _pageSizeText = "20";
    public string PageSizeText
    {
        get => _pageSizeText;
        set => this.RaiseAndSetIfChanged(ref _pageSizeText, value);
    }

    private string _goToPageText = "1";
    public string GoToPageText
    {
        get => _goToPageText;
        set => this.RaiseAndSetIfChanged(ref _goToPageText, value);
    }

    public ObservableCollection<User> Users { get; } = new();

    public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };


    public UserManagementPageViewModel(UserManagementService userManagementService)
    {
        _userManagementService = userManagementService;
        ShowAddUserDialogCommand = ReactiveCommand.CreateFromTask(ShowAddUserDialogAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(LoadUsersAsync);
        DeleteUserCommand = ReactiveCommand.CreateFromTask<User>(DeleteByIdAsync);
        EditUserCommand = ReactiveCommand.CreateFromTask<User>(ShowEditUserDialogAsync);
        SearchCommand = ReactiveCommand.CreateFromTask(SearchUsersAsync);
        FirstPageCommand = ReactiveCommand.CreateFromTask(GoToFirstPageAsync);
        PreviousPageCommand = ReactiveCommand.CreateFromTask(GoToPreviousPageAsync);
        NextPageCommand = ReactiveCommand.CreateFromTask(GoToNextPageAsync);
        LastPageCommand = ReactiveCommand.CreateFromTask(GoToLastPageAsync);
        GoToPageCommand = ReactiveCommand.CreateFromTask(GoToSpecificPageAsync);
        
        // 监听PageSize属性变化
        this.WhenAnyValue(x => x.PageSize)
            .Subscribe(async _ => await OnPageSizeChangedAsync());
            
        _ = LoadUsersAsync();
    }


    private async Task DeleteByIdAsync(User user)
    {
        // 从数据库删除用户
        var deleted = await _userManagementService.DeleteUserAsync(user.Id);
        if (deleted)
        {
            Console.WriteLine(user.ToString());
            Users.Remove(user);
        }
    }

    private async Task ShowAddUserDialogAsync()
    {
        var dialog = new ContentDialog()
        {
            Title = "添加用户",
            PrimaryButtonText = "添加",
            SecondaryButtonText = "清空",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new AddUserDialogContentViewModel(dialog, _userManagementService);
        dialog.Content = new AddUserDialogContent()
        {
            DataContext = viewModel
        };

        await dialog.ShowAsync();
        await LoadUsersAsync();
    }

    private async Task ShowEditUserDialogAsync(User user)
    {
        var dialog = new ContentDialog()
        {
            Title = "编辑用户",
            PrimaryButtonText = "保存",
            SecondaryButtonText = "重置",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        var viewModel = new EditUserDialogContentViewModel(dialog, _userManagementService, user);
        dialog.Content = new EditUserDialogContent()
        {
            DataContext = viewModel
        };

        var result = await dialog.ShowAsync();
        if (result == ContentDialogResult.Primary)
        {
            await LoadUsersAsync();
        }
    }

    private async Task LoadUsersAsync()
    {
        //可选：添加逻辑防止重复加载
        if (_isLoading) return;
        _isLoading = true;

        Users.Clear();
        
        // 获取分页数据
        var usersFromDb = await _userManagementService.FindByPageAsync(CurrentPage, PageSize);
        var totalCount = await _userManagementService.CountTotalUsersAsync();
        
        // 更新分页信息
        TotalItems = totalCount;
        TotalPages = (int)Math.Ceiling((double)totalCount / PageSize);
        UpdatePaginationState();
        
        foreach (var user in usersFromDb)
        {
            Users.Add(user);
        }

        _isLoading = false;
    }

    private async Task SearchUsersAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            CurrentPage = 1;
            await LoadUsersAsync();
            return;
        }
        
        if (_isLoading) return;
        _isLoading = true;

        Users.Clear();
        
        // 获取搜索分页数据
        var result = await _userManagementService.SearchUsersByPageAsync(SearchText, CurrentPage, PageSize);
        var totalCount = await _userManagementService.CountSearchResultsAsync(SearchText);
        
        // 更新分页信息
        TotalItems = totalCount;
        TotalPages = (int)Math.Ceiling((double)totalCount / PageSize);
        UpdatePaginationState();
        
        if (result.Count == 0 && CurrentPage == 1)
        {
            var dialog = new FluentAvalonia.UI.Controls.ContentDialog()
            {
                Title = "提示",
                Content = "没有查询到相关用户！",
                PrimaryButtonText = "好的"
            };
            await dialog.ShowAsync();
        }
        else
        {
            foreach (var user in result)
                Users.Add(user);
        }

        _isLoading = false;
    }

    private void UpdatePaginationState()
    {
        IsFirstPage = CurrentPage <= 1;
        IsLastPage = CurrentPage >= TotalPages;
        GoToPageText = CurrentPage.ToString();
    }

    private async Task GoToFirstPageAsync()
    {
        if (CurrentPage != 1)
        {
            CurrentPage = 1;
            await RefreshCurrentPageAsync();
        }
    }

    private async Task GoToPreviousPageAsync()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            await RefreshCurrentPageAsync();
        }
    }

    private async Task GoToNextPageAsync()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            await RefreshCurrentPageAsync();
        }
    }

    private async Task GoToLastPageAsync()
    {
        if (CurrentPage != TotalPages && TotalPages > 0)
        {
            CurrentPage = TotalPages;
            await RefreshCurrentPageAsync();
        }
    }

    private async Task GoToSpecificPageAsync()
    {
        if (int.TryParse(GoToPageText, out int targetPage))
        {
            if (targetPage >= 1 && targetPage <= TotalPages && targetPage != CurrentPage)
            {
                CurrentPage = targetPage;
                await RefreshCurrentPageAsync();
                return;
            }
            if (targetPage == CurrentPage)
            {
                return;
            }
        }
        GoToPageText = CurrentPage.ToString();
        var dialog = new ContentDialog()
        {
            Title = "错误",
            Content = "请输入有效的页码！",
            PrimaryButtonText = "好的",
            DefaultButton = ContentDialogButton.Primary
        };
        await dialog.ShowAsync();
    }

    private async Task RefreshCurrentPageAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadUsersAsync();
        }
        else
        {
            await SearchUsersAsync();
        }
    }

    public async Task OnPageSizeChangedAsync()
    {
        CurrentPage = 1; // 重置到第一页
        await RefreshCurrentPageAsync();
    }
}