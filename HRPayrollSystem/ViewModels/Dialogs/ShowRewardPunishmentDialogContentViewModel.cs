using System;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public partial class ShowRewardPunishmentDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly RewardPunishment _rewardPunishment;

    public string EmployeeName => _rewardPunishment.EmployeeName ?? "未知";
    public string EmployeeNumber => _rewardPunishment.EmployeeNumber ?? "未知";
    public string Type => _rewardPunishment.Type;
    public decimal Amount => _rewardPunishment.Amount;
    public string Reason => _rewardPunishment.Reason ?? "无";
    public DateTime RecordDate => _rewardPunishment.RecordDate;
    public DateTime CreatedAt => _rewardPunishment.CreatedAt;
    public DateTime UpdatedAt => _rewardPunishment.UpdatedAt;

    public ShowRewardPunishmentDialogContentViewModel(ContentDialog dialog, RewardPunishment rewardPunishment)
    {
        _dialog = dialog;
        _rewardPunishment = rewardPunishment;

        // 设置对话框关闭事件
        dialog.PrimaryButtonClick += (sender, args) =>
        {
            args.Cancel = true;
            _dialog.Hide(ContentDialogResult.Primary);
        };
    }
} 