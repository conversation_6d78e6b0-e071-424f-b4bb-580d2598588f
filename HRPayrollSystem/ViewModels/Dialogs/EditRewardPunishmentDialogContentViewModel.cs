using System;
using System.Collections.Generic;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;
using System.Linq;
using System.Reactive.Concurrency;
using System.Reactive.Linq;

namespace HRPayrollSystem.ViewModels.Dialogs;

public partial class EditRewardPunishmentDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly RewardPunishmentManagementService _rewardPunishmentManagementService;
    private readonly UserManagementService _userManagementService;
    private readonly RewardPunishment _originalRewardPunishment;
    private bool _isInitializing = true;
    
    private bool _allowSearch = false;
    public bool AllowSearch
    {
        get => _allowSearch;
        set => this.RaiseAndSetIfChanged(ref _allowSearch, value);
    }

    private string _employeeInput = "";
    public string EmployeeInput
    {
        get => _employeeInput;
        set => this.RaiseAndSetIfChanged(ref _employeeInput, value);
    }

    private IEnumerable<string> _searchResults = Array.Empty<string>();
    public IEnumerable<string> SearchResults
    {
        get => _searchResults;
        set => this.RaiseAndSetIfChanged(ref _searchResults, value);
    }
    
    private string _searchText = string.Empty;
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }
    
    private bool _isDropDownOpen = false;
    public bool IsDropDownOpen
    {
        get => _isDropDownOpen;
        set => this.RaiseAndSetIfChanged(ref _isDropDownOpen, value);
    }

    private string _type = "";
    public string Type
    {
        get => _type;
        set => this.RaiseAndSetIfChanged(ref _type, value);
    }

    private decimal? _amount;
    public decimal? Amount
    {
        get => _amount;
        set => this.RaiseAndSetIfChanged(ref _amount, value);
    }

    private string? _reason;
    public string? Reason
    {
        get => _reason;
        set => this.RaiseAndSetIfChanged(ref _reason, value);
    }

    private DateTime _recordDate;
    public DateTime RecordDate
    {
        get => _recordDate;
        set => this.RaiseAndSetIfChanged(ref _recordDate, value);
    }

    public List<string> TypeOptions { get; } = new() { "奖励", "处罚" };

    public EditRewardPunishmentDialogContentViewModel(ContentDialog dialog, RewardPunishmentManagementService rewardPunishmentManagementService, UserManagementService userManagementService, RewardPunishment rewardPunishment)
    {
        _dialog = dialog;
        _rewardPunishmentManagementService = rewardPunishmentManagementService;
        _userManagementService = userManagementService;
        _originalRewardPunishment = rewardPunishment;

        // 先加载数据
        LoadData();
        

        // 然后设置搜索功能
        this.WhenAnyValue(x => x.SearchText)
            .Throttle(TimeSpan.FromMilliseconds(300), RxApp.TaskpoolScheduler)
            .Where(txt => !string.IsNullOrWhiteSpace(txt) && !_isInitializing && AllowSearch)
            .DistinctUntilChanged()
            .SelectMany(_userManagementService.SearchTop10EmployeeNumbersAndNamesAsync)   // 并发时只保留最后一次
            .ObserveOn(RxApp.MainThreadScheduler)
            .Subscribe(list =>
            {
                SearchResults = list;
                IsDropDownOpen = list.Any();   // 有结果才打开
            });

        // 设置对话框按钮事件
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;
            await SaveAsync();
        };

        dialog.SecondaryButtonClick += (sender, args) =>
        {
            args.Cancel = true;
            Reset();
        };

        dialog.Closed += DialogOnClosed;
    }

    private void DialogOnClosed(ContentDialog sender, ContentDialogClosedEventArgs args)
    {
        _dialog.Closed -= DialogOnClosed;
        if (args.Result == ContentDialogResult.Primary)
        {
            var resultHint = new ContentDialog()
            {
                Content = "奖罚记录更新成功",
                Title = "操作成功",
                PrimaryButtonText = "好的"
            };
            _ = resultHint.ShowAsync();
        }
    }

    private void LoadData()
    {
        // 设置完整的员工信息格式（工号 + 姓名）
        var employeeInfo = "";
        if (!string.IsNullOrWhiteSpace(_originalRewardPunishment.EmployeeNumber) && 
            !string.IsNullOrWhiteSpace(_originalRewardPunishment.EmployeeName))
        {
            employeeInfo = $"{_originalRewardPunishment.EmployeeNumber} {_originalRewardPunishment.EmployeeName}";
        }
        else if (!string.IsNullOrWhiteSpace(_originalRewardPunishment.EmployeeName))
        {
            employeeInfo = _originalRewardPunishment.EmployeeName;
        }
        
        EmployeeInput = employeeInfo;
        SearchText = employeeInfo;
        Type = _originalRewardPunishment.Type;
        Amount = _originalRewardPunishment.Amount;
        Reason = _originalRewardPunishment.Reason;
        RecordDate = _originalRewardPunishment.RecordDate.Date;
        
        // 确保初始时不显示下拉列表
        IsDropDownOpen = false;
        SearchResults = Array.Empty<string>();
    }

    private async Task SaveAsync()
    {
        if (string.IsNullOrWhiteSpace(EmployeeInput))
        {
            var errorDialog = new ContentDialog()
            {
                Title = "输入错误",
                Content = "请重新输入！",
                PrimaryButtonText = "确定"
            };
            await errorDialog.ShowAsync();
            return;
        }

        // 验证员工是否存在
        var employee = await _rewardPunishmentManagementService.FindEmployeeByNumberAsync(EmployeeInput.Split(" ")[0]);
        if (employee == null)
        {
            var errorDialog = new ContentDialog()
            {
                Title = "该员工不存在！",
                Content = $"未找到员工：{EmployeeInput}",
                PrimaryButtonText = "确定"
            };
            await errorDialog.ShowAsync();
            return;
        }

        if (Amount is null or <= 0)
        {
            var errorDialog = new ContentDialog()
            {
                Title = "金额错误",
                Content = "请重新输入！",
                PrimaryButtonText = "确定"
            };
            await errorDialog.ShowAsync();
            return;
        }

        var rewardPunishment = new RewardPunishment
        {
            Id = _originalRewardPunishment.Id,
            EmployeeId = employee.Id,
            Type = Type,
            Amount = Amount ?? 0,
            Reason = Reason,
            RecordDate = RecordDate
        };

        var success = await _rewardPunishmentManagementService.UpdateRewardPunishmentAsync(rewardPunishment);
        if (success)
        {
            _dialog.Hide(ContentDialogResult.Primary);
        }
        else
        {
            var errorDialog = new ContentDialog()
            {
                Title = "错误",
                Content = "输出错误，请重新输入！",
                PrimaryButtonText = "确定"
            };
            await errorDialog.ShowAsync();
        }
    }

    private void Reset()
    {
        LoadData();
        SearchResults = Array.Empty<string>();
        IsDropDownOpen = false;
    }
} 