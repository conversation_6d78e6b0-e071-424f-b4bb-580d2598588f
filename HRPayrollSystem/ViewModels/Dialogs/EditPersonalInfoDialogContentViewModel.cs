using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class EditPersonalInfoDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly EmployeeManagementService _employeeManagementService;
    private readonly Employee _originalEmployee;

    private int _id;
    private string _employeeNumber = "";
    private string _name = "";
    private string _gender = "";
    private string _idCard = "";
    private DateTime _birthday = DateTime.Today;
    private string _phone = "";
    private string _email = "";
    private string _address = "";

    public List<string> GenderOptions { get; } = new() { "男", "女" };

    public EditPersonalInfoDialogContentViewModel(ContentDialog dialog, EmployeeManagementService employeeManagementService, Employee employee)
    {
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        _dialog = dialog;
        _employeeManagementService = employeeManagementService;
        _originalEmployee = employee;
        
        // 初始化数据
        Id = employee.Id;
        EmployeeNumber = employee.EmployeeNumber ?? "";
        Name = employee.Name ?? "";
        Gender = employee.Gender ?? "";
        IdCard = employee.IdCard ?? "";
        Birthday = employee.Birthday ?? DateTime.Today;
        Phone = employee.Phone ?? "";
        Email = employee.Email ?? "";
        Address = employee.Address ?? "";

        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;

            // 验证必填字段
            if (string.IsNullOrWhiteSpace(EmployeeNumber) || string.IsNullOrWhiteSpace(Name))
            {
                var errorDialog = new ContentDialog
                {
                    Content = "员工编号和姓名为必填项，请填写完整",
                    Title = "验证失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = errorDialog.ShowAsync();
                return;
            }

            var updatedEmployee = new Employee
            {
                Id = Id,
                EmployeeNumber = EmployeeNumber,
                Name = Name,
                Gender = Gender,
                IdCard = IdCard,
                Birthday = Birthday,
                Phone = Phone,
                Email = Email,
                Address = Address,
                DepartmentId = _originalEmployee.DepartmentId,
                PositionId = _originalEmployee.PositionId,
                HireDate = _originalEmployee.HireDate,
                ProbationEndDate = _originalEmployee.ProbationEndDate,
                Status = _originalEmployee.Status,
                BaseSalary = _originalEmployee.BaseSalary,
                ResignationDate = _originalEmployee.ResignationDate,
                ResignationReason = _originalEmployee.ResignationReason,
                CreatedAt = _originalEmployee.CreatedAt,
                UpdatedAt = DateTime.Now
            };

            var succeeded = await _employeeManagementService.UpdateEmployeeAsync(updatedEmployee);
            if (!succeeded)
            {
                var resultHint = new ContentDialog
                {
                    Content = "更新个人信息失败",
                    Title = "更新失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
            else
            {
                _dialog.Hide(ContentDialogResult.Primary);
                var successHint = new ContentDialog
                {
                    Content = "个人信息更新成功",
                    Title = "更新成功",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = successHint.ShowAsync();
            }
        };

        dialog.SecondaryButtonClick += (sender, args) =>
        {
            // 重置为原始值
            Id = _originalEmployee.Id;
            EmployeeNumber = _originalEmployee.EmployeeNumber ?? "";
            Name = _originalEmployee.Name ?? "";
            Gender = _originalEmployee.Gender ?? "";
            IdCard = _originalEmployee.IdCard ?? "";
            Birthday = _originalEmployee.Birthday ?? DateTime.Today;
            Phone = _originalEmployee.Phone ?? "";
            Email = _originalEmployee.Email ?? "";
            Address = _originalEmployee.Address ?? "";
            args.Cancel = true;
        };
    }

    public int Id
    {
        get => _id;
        set => this.RaiseAndSetIfChanged(ref _id, value);
    }

    public string EmployeeNumber
    {
        get => _employeeNumber;
        set => this.RaiseAndSetIfChanged(ref _employeeNumber, value);
    }

    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }

    public string Gender
    {
        get => _gender;
        set => this.RaiseAndSetIfChanged(ref _gender, value);
    }

    public string IdCard
    {
        get => _idCard;
        set => this.RaiseAndSetIfChanged(ref _idCard, value);
    }

    public DateTime Birthday
    {
        get => _birthday;
        set => this.RaiseAndSetIfChanged(ref _birthday, value);
    }

    public string Phone
    {
        get => _phone;
        set => this.RaiseAndSetIfChanged(ref _phone, value);
    }

    public string Email
    {
        get => _email;
        set => this.RaiseAndSetIfChanged(ref _email, value);
    }

    public string Address
    {
        get => _address;
        set => this.RaiseAndSetIfChanged(ref _address, value);
    }
}
