﻿using System;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class ShowEmployeeDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly Employee _employee;

    private string _employeeNumber = "";
    public string EmployeeNumber
    {
        get => _employeeNumber;
        set => this.RaiseAndSetIfChanged(ref _employeeNumber, value);
    }

    private string _name = "";
    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }

    private string _gender = "";
    public string Gender
    {
        get => _gender;
        set => this.RaiseAndSetIfChanged(ref _gender, value);
    }

    private string _idCard = "";
    public string IdCard
    {
        get => _idCard;
        set => this.RaiseAndSetIfChanged(ref _idCard, value);
    }

    private DateTime _birthday = DateTime.Today;
    public DateTime Birthday
    {
        get => _birthday;
        set => this.RaiseAndSetIfChanged(ref _birthday, value);
    }

    private string _phone = "";
    public string Phone
    {
        get => _phone;
        set => this.RaiseAndSetIfChanged(ref _phone, value);
    }

    private string _email = "";
    public string Email
    {
        get => _email;
        set => this.RaiseAndSetIfChanged(ref _email, value);
    }

    private string _address = "";
    public string Address
    {
        get => _address;
        set => this.RaiseAndSetIfChanged(ref _address, value);
    }

    private string _department = "";
    public string Department
    {
        get => _department;
        set => this.RaiseAndSetIfChanged(ref _department, value);
    }

    private string _position = "";
    public string Position
    {
        get => _position;
        set => this.RaiseAndSetIfChanged(ref _position, value);
    }

    private DateTime _hireDate = DateTime.Today;
    public DateTime HireDate
    {
        get => _hireDate;
        set => this.RaiseAndSetIfChanged(ref _hireDate, value);
    }

    private DateTime _probationEndDate = DateTime.Today.AddMonths(3);
    public DateTime ProbationEndDate
    {
        get => _probationEndDate;
        set => this.RaiseAndSetIfChanged(ref _probationEndDate, value);
    }

    private string _status = "在职";
    public string Status
    {
        get => _status;
        set => this.RaiseAndSetIfChanged(ref _status, value);
    }

    private decimal _baseSalary;
    public decimal BaseSalary
    {
        get => _baseSalary;
        set => this.RaiseAndSetIfChanged(ref _baseSalary, value);
    }

    private DateTime? _resignationDate;
    public DateTime? ResignationDate
    {
        get => _resignationDate;
        set => this.RaiseAndSetIfChanged(ref _resignationDate, value);
    }

    private string _resignationReason = "";
    public string ResignationReason
    {
        get => _resignationReason;
        set => this.RaiseAndSetIfChanged(ref _resignationReason, value);
    }

    public ShowEmployeeDialogContentViewModel(ContentDialog dialog, Employee employee)
    {
        if (dialog is null)
            throw new ArgumentNullException(nameof(dialog));

        _dialog = dialog;
        _employee = employee;

        // 初始化数据
        EmployeeNumber = employee.EmployeeNumber ?? "";
        Name = employee.Name ?? "";
        Gender = employee.Gender ?? "";
        IdCard = employee.IdCard ?? "";
        Birthday = employee.Birthday ?? DateTime.Today;
        Phone = employee.Phone ?? "";
        Email = employee.Email ?? "";
        Address = employee.Address ?? "";
        Department = employee.Department ?? "";
        Position = employee.Position ?? "";
        HireDate = employee.HireDate ?? DateTime.Today;
        ProbationEndDate = employee.ProbationEndDate ?? DateTime.Today.AddMonths(3);
        Status = employee.Status ?? "在职";
        BaseSalary = employee.BaseSalary;
        ResignationDate = employee.ResignationDate;
        ResignationReason = employee.ResignationReason ?? "";
    }
}