using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class EditDepartmentDialogContentViewModel : ViewModelBase
{
    private string _name = string.Empty;
    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }
    
    private int _parentId = 0;
    public int ParentId
    {
        get => _parentId;
        set => this.RaiseAndSetIfChanged(ref _parentId, value);
    }

    private string _parentName = string.Empty;
    public string ParentName
    {
        get => _parentName;
        set => this.RaiseAndSetIfChanged(ref _parentName, value);
    }
    
    private string? _parentNameWithId;
    public string? ParentNameWithId
    {
        get => _parentNameWithId;
        set => this.RaiseAndSetIfChanged(ref _parentNameWithId, value);
    }
    
    private readonly ContentDialog _dialog;
    private readonly DepartmentManagementService _departmentManagementService;
    private readonly Department _originalDepartment;

    public List<Department> AvailableDepartments { get; private set; } = new();
    public ObservableCollection<string> DepartmentOptions { get; private set; } = new();

    public EditDepartmentDialogContentViewModel(ContentDialog dialog, DepartmentManagementService departmentManagementService, Department department)
    {
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        _dialog = dialog;
        _departmentManagementService = departmentManagementService;
        _originalDepartment = department;

        // 初始化数据
        Name = department.Name ?? "";
        ParentId = department.ParentId;
        ParentName = department.ParentName ?? "";
        

        dialog.Closed += DialogOnClosed;
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;
            UpdateParentIdFromName();
            if (string.IsNullOrWhiteSpace(Name))
            {
                var resultHint = new ContentDialog()
                {
                    Content = "部门名称不能为空",
                    Title = "操作失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
                return;
            }
            var updatedDepartment = new Department
            {
                Id = _originalDepartment.Id,
                Name = Name,
                ParentId = ParentId
            };
            
            var succeeded = await _departmentManagementService.UpdateDepartmentAsync(updatedDepartment);
            if (!succeeded)
            {
                var resultHint = new ContentDialog()
                {
                    Content = "更新部门失败，请检查输入",
                    Title = "操作失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
            else
            {
                _dialog.Hide();
            }
        };
        _ = LoadDepartmentOptionsAsync();
        
    }

    private async Task LoadDepartmentOptionsAsync()
    {
        try
        {
            
            // 先清空旧数据，以防重复加载
            DepartmentOptions.Clear();
            var available = await _departmentManagementService.GetAllDepartmentsForSelectionAsync();
            this.AvailableDepartments = available; // 保存原始数据用于后续解析
            // 添加顶级选项
            DepartmentOptions.Add("无");
            // 遍历并添加其他选项，排除当前编辑的部门
            var optionsToAdd = available
                .Where(d => d.Id != _originalDepartment.Id) // 排除当前编辑的部门
                .Select(d => $"{d.Name} (ID: {d.Id})");

            foreach (var option in optionsToAdd)
            {
                DepartmentOptions.Add(option);
                Console.WriteLine($"添加选项: {option} - 线程ID: {System.Threading.Thread.CurrentThread.ManagedThreadId}");
            }
            
            Console.WriteLine("部门选项加载成功，已添加到 ObservableCollection。");
            if (ParentId == 0)
            {
                ParentNameWithId = "无";
            }
            else
            {
                ParentNameWithId = $"{ParentName} (ID: {ParentId})";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载部门选项时发生错误: {ex.Message}");
        }
    }

    private void DialogOnClosed(ContentDialog sender, ContentDialogClosedEventArgs args)
    {
        _dialog.Closed -= DialogOnClosed;
        if (args.Result == ContentDialogResult.Primary)
        {
            var resultHint = new ContentDialog()
            {
                Content = "部门更新成功",
                Title = "更新部门成功",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            _ = resultHint.ShowAsync();
        }
    }


    public void UpdateParentIdFromName()
    {
        if (ParentNameWithId == "无")
        {
            ParentId = 0;
            ParentName = "无";
            return;
        }

        var match = AvailableDepartments.FirstOrDefault(d => $"{d.Name} (ID: {d.Id})" == ParentNameWithId);
        if (match != null)
        {
            ParentId = match.Id;
            ParentName = match.Name ?? "";
        }
    }
} 