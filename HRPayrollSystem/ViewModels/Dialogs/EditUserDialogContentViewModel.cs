using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive.Linq;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class EditUserDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly UserManagementService _userManagementService;
    private readonly User _originalUser;
    private bool _isInitializing = true;
    
    private bool _allowSearch = false;
    public bool AllowSearch
    {
        get => _allowSearch;
        set => this.RaiseAndSetIfChanged(ref _allowSearch, value);
    }

    private string _employeeInput = "";
    public string EmployeeInput
    {
        get => _employeeInput;
        set => this.RaiseAndSetIfChanged(ref _employeeInput, value);
    }

    private IEnumerable<string> _searchResults = Array.Empty<string>();
    public IEnumerable<string> SearchResults
    {
        get => _searchResults;
        set => this.RaiseAndSetIfChanged(ref _searchResults, value);
    }
    
    private string _searchText = string.Empty;
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }
    
    private bool _isDropDownOpen;
    public bool IsDropDownOpen
    {
        get => _isDropDownOpen;
        set => this.RaiseAndSetIfChanged(ref _isDropDownOpen, value);
    }

    public List<string> RoleOptions { get; } = new() { "管理员", "HR专员", "员工" };

    private int _id;
    public int Id
    {
        get => _id;
        set => this.RaiseAndSetIfChanged(ref _id, value);
    }

    private string _employeeNumber = "";
    public string EmployeeNumber
    {
        get => _employeeNumber;
        set => this.RaiseAndSetIfChanged(ref _employeeNumber, value);
    }

    private string _role = "";
    public string Role
    {
        get => _role;
        set => this.RaiseAndSetIfChanged(ref _role, value);
    }

    private string _password = "";
    public string Password
    {
        get => _password;
        set => this.RaiseAndSetIfChanged(ref _password, value);
    }

    public EditUserDialogContentViewModel(ContentDialog dialog, UserManagementService userManagementService, User user)
    {
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        _dialog = dialog;
        _userManagementService = userManagementService;
        _originalUser = user;
        
        // 初始化数据
        Id = user.Id;
        EmployeeNumber = user.EmployeeNumber ?? "";
        Role = user.Role ?? "";
        Password = ""; // 密码字段为空，表示不修改密码
        
        // 初始化搜索相关属性
        EmployeeInput = user.EmployeeNumber ?? "";
        SearchText = user.EmployeeNumber ?? "";
        
        // 设置搜索逻辑
        this.WhenAnyValue(x => x.SearchText)
            .Where(_ => AllowSearch)
            .Throttle(TimeSpan.FromMilliseconds(300))
            .Where(txt => !string.IsNullOrWhiteSpace(txt))
            .DistinctUntilChanged()
            .SelectMany(userManagementService.SearchTop10EmployeeNumbersAndNamesAsync)
            .ObserveOn(RxApp.MainThreadScheduler)
            .Subscribe(list =>
            {
                SearchResults = list;
                IsDropDownOpen = list.Any();
            });
            
        // 监听员工输入变化，更新员工编号
        this.WhenAnyValue(x => x.EmployeeInput)
            .Subscribe(input =>
            {
                if (!string.IsNullOrWhiteSpace(input))
                {
                    EmployeeNumber = input.Split(' ')[0]; // 取第一部分作为员工编号
                }
            });
        
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            // 验证输入
            if (string.IsNullOrWhiteSpace(EmployeeNumber))
            {
                args.Cancel = true;
                var errorDialog = new ContentDialog
                {
                    Content = "员工编号不能为空",
                    Title = "输入错误",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = errorDialog.ShowAsync();
                return;
            }

            if (string.IsNullOrWhiteSpace(Role))
            {
                args.Cancel = true;
                var errorDialog = new ContentDialog
                {
                    Content = "角色不能为空",
                    Title = "输入错误",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = errorDialog.ShowAsync();
                return;
            }

            var updatedUser = new User
            {
                Id = Id,
                EmployeeNumber = EmployeeNumber,
                Role = Role,
                // 如果密码字段不为空，则更新密码；否则保留原密码
                PasswordHash = string.IsNullOrWhiteSpace(Password) 
                    ? _originalUser.PasswordHash 
                    : _userManagementService.HashPassword(Password),
                CreatedAt = _originalUser.CreatedAt,
                UpdatedAt = DateTime.Now
            };

            var succeeded = await _userManagementService.UpdateUserAsync(updatedUser);
            if (!succeeded)
            {
                args.Cancel = true;
                var resultHint = new ContentDialog
                {
                    Content = "更新用户信息失败",
                    Title = "操作失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
        };

        dialog.SecondaryButtonClick += (sender, args) =>
        {
            // 重置为原始值
            Id = _originalUser.Id;
            EmployeeNumber = _originalUser.EmployeeNumber ?? "";
            Role = _originalUser.Role ?? "";
            Password = "";
            args.Cancel = true;
        };
    }
}
