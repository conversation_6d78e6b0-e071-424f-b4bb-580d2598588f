using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Services;
using HRPayrollSystem.Models;  
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class AddDepartmentDialogContentViewModel : ViewModelBase
{
    private string _name = "";
    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }
    
    private int _parentId = 0;
    public int ParentId
    {
        get => _parentId;
        set => this.RaiseAndSetIfChanged(ref _parentId, value);
    }

    private string _parentName = "";
    public string ParentName
    {
        get => _parentName;
        set => this.RaiseAndSetIfChanged(ref _parentName, value);
    }
    
    private string? _parentNameWithId;
    public string? ParentNameWithId
    {
        get => _parentNameWithId;
        set => this.RaiseAndSetIfChanged(ref _parentNameWithId, value);
    }
    
    private readonly ContentDialog _dialog;
    private readonly DepartmentManagementService _departmentManagementService;

    public List<Department> AvailableDepartments { get; private set; } = new();
    public ObservableCollection<string> DepartmentOptions { get; private set; } = new();

    public AddDepartmentDialogContentViewModel(ContentDialog dialog, DepartmentManagementService departmentManagementService)
    {
        
        
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        this._dialog = dialog;
        this._departmentManagementService = departmentManagementService;
        dialog.Closed += DialogOnClosed;
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;
            UpdateParentIdFromName();
            
            if (string.IsNullOrWhiteSpace(Name) || ParentNameWithId is null)
            {
                var resultHint = new ContentDialog()
                {
                    Content = "添加部门失败，请检查输入",
                    Title = "添加部门失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
                return;
            }
            var department = new Models.Department
            {
                Name = Name,
                ParentId = ParentId
            };
            var succeeded = await departmentManagementService.InsertDepartmentAsync(department);
            if (!succeeded)
            {
                var resultHint = new ContentDialog()
                {
                    Content = "添加部门失败，请检查输入",
                    Title = "添加部门失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
            else
            {
                _dialog.Hide();
            }
        };
        dialog.SecondaryButtonClick += (sender, args) =>
        {
            Name = string.Empty;
            ParentId = 0;
            ParentName = string.Empty;
            ParentNameWithId = string.Empty;
            args.Cancel = true;
        };
        _ = LoadDepartmentOptionsAsync();
        // 加载部门选项
    }

    private void DialogOnClosed(ContentDialog sender, ContentDialogClosedEventArgs args)
    {
        _dialog.Closed -= DialogOnClosed;
        if (args.Result == ContentDialogResult.Primary)
        {
            var resultHint = new ContentDialog()
            {
                Content = "部门添加成功",
                Title = "操作成功",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            _ = resultHint.ShowAsync();
        }
    }
    
    private async Task LoadDepartmentOptionsAsync()
    {
        try
        {
            // 先清空旧数据，以防重复加载
            DepartmentOptions.Clear();

            var available = await _departmentManagementService.GetAllDepartmentsForSelectionAsync();
            this.AvailableDepartments = available; // 保存原始数据用于后续解析
            
            // ✅ **步骤 2: 修改加载逻辑，直接操作 ObservableCollection**
            
            // 添加顶级选项
            DepartmentOptions.Add("无");

            // 遍历并添加其他选项
            var optionsToAdd = available
                .Where(d => d.Name != Name) 
                .Select(d => $"{d.Name} (ID: {d.Id})");

            foreach (var option in optionsToAdd)
            {
                DepartmentOptions.Add(option);
            }
            
            Console.WriteLine("部门选项加载成功，已添加到 ObservableCollection。");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载部门选项时发生错误: {ex.Message}");
        }
    }



    public void UpdateParentIdFromName()
    {
        if (ParentNameWithId == "无")
        {
            ParentId = 0;
            ParentName = "无";
            return;
        }

        var match = AvailableDepartments.FirstOrDefault(d => $"{d.Name} (ID: {d.Id})" == ParentNameWithId);
        if (match != null)
        {
            ParentId = match.Id;
            ParentName = match.Name ?? "";
        }
    }
} 