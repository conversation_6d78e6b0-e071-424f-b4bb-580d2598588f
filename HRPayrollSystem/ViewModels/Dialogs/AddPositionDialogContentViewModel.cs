using System;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class AddPositionDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;

    private string _name = "";
    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }
    
    private string _description = "";
    public string Description
    {
        get => _description;
        set => this.RaiseAndSetIfChanged(ref _description, value);
    }

    public AddPositionDialogContentViewModel(ContentDialog dialog, PositionManagementService positionManagementService)
    {
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        this._dialog = dialog;
        dialog.Closed += DialogOnClosed;
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;
            if (string.IsNullOrWhiteSpace(Name))
            {
                var resultHint = new ContentDialog()
                {
                    Content = "岗位名称不能为空",
                    Title = "添加岗位失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
                return;
            }
            var position = new Models.Position
            {
                Name = Name,
                Description = Description
            };
            var succeeded = await positionManagementService.InsertPositionAsync(position);
            if (!succeeded)
            {
                var resultHint = new ContentDialog()
                {
                    Content = "添加岗位失败，请检查输入",
                    Title = "添加岗位失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
            else
            {
                _dialog.Hide();
            }
        };
        dialog.SecondaryButtonClick += (sender, args) =>
        {
            Name = string.Empty;
            Description = string.Empty;
            args.Cancel = true;
        };
    }

    private void DialogOnClosed(ContentDialog sender, ContentDialogClosedEventArgs args)
    {
        _dialog.Closed -= DialogOnClosed;
        if (args.Result == ContentDialogResult.Primary)
        {
            var resultHint = new ContentDialog()
            {
                Content = "岗位添加成功",
                Title = "添加岗位成功",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            _ = resultHint.ShowAsync();
        }
    }

} 