using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive.Linq;
using DynamicData.Alias;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class AddUserDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;

    private string _employeeNumberName = "";
    public string EmployeeNumberName
    {
        get => _employeeNumberName;
        set => this.RaiseAndSetIfChanged(ref _employeeNumberName, value);
    }
    
    private string _password = "";
    public string Password
    {
        get => _password;
        set => this.RaiseAndSetIfChanged(ref _password, value);
    }
    
    private string _role = "";
    public string Role
    {
        get => _role;
        set => this.RaiseAndSetIfChanged(ref _role, value);
    }
    
    public IEnumerable<string> RoleOptions { get; } = new[] { "管理员", "人事经理", "员工" };
    
    private IEnumerable<string> _searchResults = Array.Empty<string>();
    public IEnumerable<string> SearchResults
    {
        get => _searchResults;
        set => this.RaiseAndSetIfChanged(ref _searchResults, value);
    }
    
    private string _searchText = string.Empty;
    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }
    
    private bool _isDropDownOpen;
    public bool IsDropDownOpen
    {
        get => _isDropDownOpen;
        set => this.RaiseAndSetIfChanged(ref _isDropDownOpen, value);
    }

    public AddUserDialogContentViewModel(ContentDialog dialog, UserManagementService userManagementService)
    {
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        this._dialog = dialog;
        dialog.Closed += DialogOnClosed;
        
        this.WhenAnyValue(x => x.SearchText)
            .Throttle(TimeSpan.FromMilliseconds(300), RxApp.TaskpoolScheduler)
            .Where(txt => !string.IsNullOrWhiteSpace(txt))
            .DistinctUntilChanged()
            .SelectMany(userManagementService.SearchTop10EmployeeNumbersAndNamesAsync)   // 并发时只保留最后一次
            .ObserveOn(RxApp.MainThreadScheduler)
            .Subscribe(list =>
            {
                SearchResults = list;
                IsDropDownOpen = list.Any();   // 有结果才打开
            });
        
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;
            
            // 验证所有字段都已填写
            if (string.IsNullOrWhiteSpace(EmployeeNumberName) || 
                string.IsNullOrWhiteSpace(Password) || 
                string.IsNullOrWhiteSpace(Role))
            {
                var validationHint = new ContentDialog()
                {
                    Content = "请填写所有必填字段：员工、初始密码和用户角色",
                    Title = "输入验证",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = validationHint.ShowAsync();
                return;
            }
            
            // 将 Cancel 属性设置为 true 来阻止对话框关闭
            var user = new Models.User
            {
                EmployeeNumber = EmployeeNumberName.Split(" ")[0],
                PasswordHash = userManagementService.HashPassword(Password),
                Role = Role
            };
            var succeeded = await userManagementService.InsertUserAsync(user);
            if (!succeeded)
            {
                var resultHint = new ContentDialog()
                {
                    Content = "用户与员工绑定失败，请检查输入",
                    Title = "添加用户失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
            else
            {
                _dialog.Hide();
            }

        };
        dialog.SecondaryButtonClick += (sender, args) =>
        {
            EmployeeNumberName = string.Empty;
            Password = string.Empty;
            Role = string.Empty;
            args.Cancel = true;
        };
    }

    private void DialogOnClosed(ContentDialog sender, ContentDialogClosedEventArgs args)
    {
        _dialog.Closed -= DialogOnClosed;
        if (args.Result == ContentDialogResult.Primary)
        {
            var resultHint = new ContentDialog()
            {
                Content = "用户与员工绑定成功",
                Title = "操作成功",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            _ = resultHint.ShowAsync();
        }

    }
    
}