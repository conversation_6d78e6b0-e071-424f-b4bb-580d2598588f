using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs
{
    public class TransferEmployeeDialogContentViewModel : ViewModelBase
    {
        private readonly ContentDialog _dialog;
        private readonly TransferService _transferService;
        private readonly DepartmentManagementService _departmentService;
        private readonly PositionManagementService _positionService;
        private readonly Employee _employee;

        private Department? _selectedDepartment;
        public Department? SelectedDepartment
        {
            get => _selectedDepartment;
            set => this.RaiseAndSetIfChanged(ref _selectedDepartment, value);
        }

        private Position? _selectedPosition;
        public Position? SelectedPosition
        {
            get => _selectedPosition;
            set => this.RaiseAndSetIfChanged(ref _selectedPosition, value);
        }

        private string _currentDepartmentName = "";
        public string CurrentDepartmentName
        {
            get => _currentDepartmentName;
            set => this.RaiseAndSetIfChanged(ref _currentDepartmentName, value);
        }

        private string _currentPositionName = "";
        public string CurrentPositionName
        {
            get => _currentPositionName;
            set => this.RaiseAndSetIfChanged(ref _currentPositionName, value);
        }

        private DateTime _transferDate = DateTime.Today;
        public DateTime TransferDate
        {
            get => _transferDate;
            set => this.RaiseAndSetIfChanged(ref _transferDate, value);
        }

        private string _reason = "";
        public string Reason
        {
            get => _reason;
            set => this.RaiseAndSetIfChanged(ref _reason, value);
        }

        public ObservableCollection<Department> Departments { get; } = new();
        public ObservableCollection<Position> Positions { get; } = new();

        public TransferEmployeeDialogContentViewModel(
            ContentDialog dialog,
            TransferService transferService,
            DepartmentManagementService departmentService,
            PositionManagementService positionService,
            Employee employee)
        {
            _dialog = dialog;
            _transferService = transferService;
            _departmentService = departmentService;
            _positionService = positionService;
            _employee = employee;

            _dialog.PrimaryButtonClick += OnSaveButtonClickAsync;
            _dialog.SecondaryButtonClick += OnResetButtonClickAsync;
            _dialog.CloseButtonClick += OnCloseButtonClickAsync;

            // 初始化
            _ = LoadDepartmentsAndPositionsAsync();
        }

        private async Task LoadDepartmentsAndPositionsAsync()
        {
            var departments = await _departmentService.FindAllAsync();
            var positions = await _positionService.FindAllAsync();

            // 设置当前部门和职位名称
            Department? currentDepartment = null;
            Position? currentPosition = null;

            Departments.Clear();
            foreach (var department in departments)
            {
                Departments.Add(department);
                if (_employee.DepartmentId == department.Id)
                {
                    currentDepartment = department;
                }
            }

            Positions.Clear();
            foreach (var position in positions)
            {
                Positions.Add(position);
                if (_employee.PositionId == position.Id)
                {
                    currentPosition = position;
                }
            }

            // 设置当前部门和职位的显示名称
            CurrentDepartmentName = currentDepartment?.Name ?? "未分配";
            CurrentPositionName = currentPosition?.Name ?? "未分配";

            // 默认选择当前的部门和职位
            SelectedDepartment = currentDepartment;
            SelectedPosition = currentPosition;
        }

        private async void OnSaveButtonClickAsync(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            var deferral = args.GetDeferral();

            try
            {
                args.Cancel = true;

                // 验证
                bool hasDepartmentChange = SelectedDepartment != null && SelectedDepartment.Id != _employee.DepartmentId;
                bool hasPositionChange = SelectedPosition != null && SelectedPosition.Id != _employee.PositionId;
                
                if (!hasDepartmentChange && !hasPositionChange)
                {
                    var errorDialog = new ContentDialog
                    {
                        Title = "错误",
                        Content = "请至少选择一个与当前不同的部门或职位！",
                        PrimaryButtonText = "确定"
                    };
                    await errorDialog.ShowAsync();
                    return;
                }

                // 创建调动记录
                var transfer = new TransferLog
                {
                    EmployeeId = _employee.Id,
                    TransferDate = TransferDate,
                    Reason = string.IsNullOrWhiteSpace(Reason) ? null : Reason
                };
                
                // 设置调动类型，可以同时调动部门和职位
                if (hasDepartmentChange && hasPositionChange)
                {
                    transfer.Type = "部门与职位调动";
                    transfer.OldDepartmentId = _employee.DepartmentId;
                    transfer.NewDepartmentId = SelectedDepartment?.Id;
                    transfer.OldPositionId = _employee.PositionId;
                    transfer.NewPositionId = SelectedPosition?.Id;
                }
                else if (hasDepartmentChange)
                {
                    transfer.Type = "部门调动";
                    transfer.OldDepartmentId = _employee.DepartmentId;
                    transfer.NewDepartmentId = SelectedDepartment?.Id;
                }
                else if (hasPositionChange)
                {
                    transfer.Type = "职位调动";
                    transfer.OldPositionId = _employee.PositionId;
                    transfer.NewPositionId = SelectedPosition?.Id;
                }

                bool success = await _transferService.CreateTransferAsync(transfer);

                if (success)
                {
                    var successDialog = new ContentDialog
                    {
                        Title = "成功",
                        Content = "员工调动记录已成功保存！",
                        PrimaryButtonText = "确定"
                    };
                    await successDialog.ShowAsync();
                    _dialog.Hide();
                }
                else
                {
                    var errorDialog = new ContentDialog
                    {
                        Title = "错误",
                        Content = "创建调动记录失败！",
                        PrimaryButtonText = "确定"
                    };
                    await errorDialog.ShowAsync();
                }
            }
            finally
            {
                deferral.Complete();
            }
        }

        private void OnResetButtonClickAsync(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            args.Cancel = true;
            
            // 重置回当前的部门和职位
            foreach (var department in Departments)
            {
                if (_employee.DepartmentId == department.Id)
                {
                    SelectedDepartment = department;
                    break;
                }
            }

            foreach (var position in Positions)
            {
                if (_employee.PositionId == position.Id)
                {
                    SelectedPosition = position;
                    break;
                }
            }
            
            TransferDate = DateTime.Today;
            Reason = "";
        }

        private void OnCloseButtonClickAsync(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            _dialog.Hide();
        }
    }
} 