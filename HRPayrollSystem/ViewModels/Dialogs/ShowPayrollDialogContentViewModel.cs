using System;
using System.Threading.Tasks;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs
{
    public class ShowPayrollDialogContentViewModel : ViewModelBase
    {
        private readonly PayrollManagementService _payrollManagementService;

        private int _id;
        public int Id
        {
            get => _id;
            set => this.RaiseAndSetIfChanged(ref _id, value);
        }

        private string _employeeName = "";
        public string EmployeeName
        {
            get => _employeeName;
            set => this.RaiseAndSetIfChanged(ref _employeeName, value);
        }

        private string _employeeNumber = "";
        public string EmployeeNumber
        {
            get => _employeeNumber;
            set => this.RaiseAndSetIfChanged(ref _employeeNumber, value);
        }

        private string _department = "";
        public string Department
        {
            get => _department;
            set => this.RaiseAndSetIfChanged(ref _department, value);
        }

        private string _position = "";
        public string Position
        {
            get => _position;
            set => this.RaiseAndSetIfChanged(ref _position, value);
        }

        private string _payMonth = "";
        public string PayMonth
        {
            get => _payMonth;
            set => this.RaiseAndSetIfChanged(ref _payMonth, value);
        }

        private decimal _baseSalary;
        public decimal BaseSalary
        {
            get => _baseSalary;
            set => this.RaiseAndSetIfChanged(ref _baseSalary, value);
        }

        private decimal _rewardAmount;
        public decimal RewardAmount
        {
            get => _rewardAmount;
            set => this.RaiseAndSetIfChanged(ref _rewardAmount, value);
        }

        private decimal _penaltyAmount;
        public decimal PenaltyAmount
        {
            get => _penaltyAmount;
            set => this.RaiseAndSetIfChanged(ref _penaltyAmount, value);
        }

        private decimal _attendanceDeduction;
        public decimal AttendanceDeduction
        {
            get => _attendanceDeduction;
            set => this.RaiseAndSetIfChanged(ref _attendanceDeduction, value);
        }

        private decimal _netSalary;
        public decimal NetSalary
        {
            get => _netSalary;
            set => this.RaiseAndSetIfChanged(ref _netSalary, value);
        }

        private string _status = "";
        public string Status
        {
            get => _status;
            set => this.RaiseAndSetIfChanged(ref _status, value);
        }

        private DateTime _createdAt;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set => this.RaiseAndSetIfChanged(ref _createdAt, value);
        }

        private DateTime _updatedAt;
        public DateTime UpdatedAt
        {
            get => _updatedAt;
            set => this.RaiseAndSetIfChanged(ref _updatedAt, value);
        }

        public ShowPayrollDialogContentViewModel(PayrollManagementService payrollManagementService)
        {
            _payrollManagementService = payrollManagementService;
        }

        public async Task LoadPayrollAsync(int payrollId)
        {
            var payroll = await _payrollManagementService.GetPayrollByIdAsync(payrollId);
            if (payroll != null)
            {
                Id = payroll.Id;
                EmployeeName = payroll.EmployeeName ?? "";
                EmployeeNumber = payroll.EmployeeNumber ?? "";
                Department = payroll.Department ?? "";
                Position = payroll.Position ?? "";
                PayMonth = payroll.PayMonth;
                BaseSalary = payroll.BaseSalary;
                RewardAmount = payroll.RewardAmount;
                PenaltyAmount = payroll.PenaltyAmount;
                AttendanceDeduction = payroll.AttendanceDeduction;
                NetSalary = payroll.NetSalary;
                Status = payroll.Status;
                CreatedAt = payroll.CreatedAt;
                UpdatedAt = payroll.UpdatedAt;
            }
        }
    }
} 