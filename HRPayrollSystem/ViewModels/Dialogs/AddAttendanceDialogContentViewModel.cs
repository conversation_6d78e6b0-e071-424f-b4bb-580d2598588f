using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive.Linq;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs
{
    public class AddAttendanceDialogContentViewModel : ViewModelBase
    {
        private readonly ContentDialog _dialog;
        private readonly AttendanceManagementService _attendanceService;
        private readonly UserManagementService _userManagementService;
        private readonly RewardPunishmentManagementService _rewardPunishmentManagementService;

        private Employee? _selectedEmployee;
        public Employee? SelectedEmployee
        {
            get => _selectedEmployee;
            set => this.RaiseAndSetIfChanged(ref _selectedEmployee, value);
        }

        private string _employeeInput = "";
        public string EmployeeInput
        {
            get => _employeeInput;
            set
            {
                Console.WriteLine($"[AddAttendance] SearchText changing from '{SearchText}' to '{value}'");
                this.RaiseAndSetIfChanged(ref _employeeInput, value);
            }
        }

        private IEnumerable<string> _searchResults = Array.Empty<string>();
        public IEnumerable<string> SearchResults
        {
            get => _searchResults;
            set
            {
                Console.WriteLine($"[AddAttendance] SearchResults updating with {value?.Count() ?? 0} items: [{string.Join(", ", value ?? Array.Empty<string>())}]");
                this.RaiseAndSetIfChanged(ref _searchResults, value);
            }
        }

        private bool _isDropDownOpen;
        public bool IsDropDownOpen
        {
            get => _isDropDownOpen;
            set
            {
                Console.WriteLine($"[AddAttendance] IsDropDownOpen changing from {_isDropDownOpen} to {value}");
                this.RaiseAndSetIfChanged(ref _isDropDownOpen, value);
            }
        }

        private DateTime _attendanceDate = DateTime.Today;
        public DateTime AttendanceDate
        {
            get => _attendanceDate;
            set => this.RaiseAndSetIfChanged(ref _attendanceDate, value);
        }

        private string _status = "正常";
        public string Status
        {
            get => _status;
            set => this.RaiseAndSetIfChanged(ref _status, value);
        }

        private string _notes = "";
        public string Notes
        {
            get => _notes;
            set => this.RaiseAndSetIfChanged(ref _notes, value);
        }

        // 状态选项
        public string[] StatusOptions { get; } = { "正常", "迟到", "早退", "旷工", "请假" };

        public AddAttendanceDialogContentViewModel(ContentDialog dialog,
            AttendanceManagementService attendanceService,
            UserManagementService userManagementService,
            RewardPunishmentManagementService rewardPunishmentManagementService)
        {
            _dialog = dialog;
            _attendanceService = attendanceService;
            _userManagementService = userManagementService;
            _rewardPunishmentManagementService = rewardPunishmentManagementService;

            // 设置搜索功能 - 参考奖罚管理的实现
            Console.WriteLine("[AddAttendance] Setting up search functionality");
            this.WhenAnyValue(x => x.SearchText)
                .Do(txt => Console.WriteLine($"[AddAttendance] WhenAnyValue triggered with: '{txt}'"))
                .Do(txt => Console.WriteLine($"[AddAttendance] After Throttle: '{txt}'"))
                .Where(txt => !string.IsNullOrWhiteSpace(txt))
                .Do(txt => Console.WriteLine($"[AddAttendance] After Where filter: '{txt}'"))
                .DistinctUntilChanged()
                .Do(txt => Console.WriteLine($"[AddAttendance] After DistinctUntilChanged: '{txt}', calling search service..."))
                .SelectMany(async txt =>
                {
                    Console.WriteLine($"[AddAttendance] SelectMany: Calling SearchTop10EmployeeNumbersAndNamesAsync with '{txt}'");
                    var results = await _userManagementService.SearchTop10EmployeeNumbersAndNamesAsync(txt);
                    Console.WriteLine($"[AddAttendance] SelectMany: Search service returned {results?.Count() ?? 0} results");
                    return results;
                })
                .ObserveOn(RxApp.MainThreadScheduler)
                .Do(list => Console.WriteLine($"[AddAttendance] ObserveOn MainThread: Received {list?.Count() ?? 0} results"))
                .Subscribe(list =>
                {
                    Console.WriteLine($"[AddAttendance] Subscribe: Setting SearchResults and IsDropDownOpen");
                    SearchResults = list;
                    IsDropDownOpen = list.Any();
                    Console.WriteLine($"[AddAttendance] Subscribe: IsDropDownOpen set to {list.Any()}");
                });

            // 当用户选择了一个完整的搜索结果时，尝试设置 SelectedEmployee
            this.WhenAnyValue(x => x.EmployeeInput)
                .Where(input => !string.IsNullOrWhiteSpace(input))
                .SelectMany(async input =>
                {
                    try
                    {
                        var employeeNumber = input.Split(' ')[0];
                        return await _rewardPunishmentManagementService.FindEmployeeByNumberAsync(employeeNumber);
                    }
                    catch
                    {
                        return null;
                    }
                })
                .ObserveOn(RxApp.MainThreadScheduler)
                .Subscribe(employee => SelectedEmployee = employee);

            // 设置对话框按钮事件
            dialog.PrimaryButtonClick += async (sender, args) =>
            {
                args.Cancel = true;
                await SaveAsync();
            };

            dialog.SecondaryButtonClick += (sender, args) =>
            {
                args.Cancel = true;
                Clear();
            };

            dialog.Closed += DialogOnClosed;
        }



        private async Task SaveAsync()
        {
            if (string.IsNullOrWhiteSpace(EmployeeInput))
            {
                var errorDialog = new ContentDialog()
                {
                    Title = "输入错误",
                    Content = "请重新输入！",
                    PrimaryButtonText = "确定"
                };
                await errorDialog.ShowAsync();
                return;
            }

            // 验证员工是否存在
            var employee = await _rewardPunishmentManagementService.FindEmployeeByNumberAsync(EmployeeInput.Split(" ")[0]);
            if (employee == null)
            {
                var errorDialog = new ContentDialog()
                {
                    Title = "该员工不存在！",
                    Content = $"未找到员工：{EmployeeInput}",
                    PrimaryButtonText = "确定"
                };
                await errorDialog.ShowAsync();
                return;
            }

            var attendance = new Attendance
            {
                EmployeeId = employee.Id,
                Date = AttendanceDate,
                Status = Status,
                Notes = string.IsNullOrWhiteSpace(Notes) ? null : Notes
            };

            // 检查是否已存在该员工当天的考勤记录
            var existingAttendance = await _attendanceService.FindByEmployeeAndDateAsync(
                employee.Id, AttendanceDate);

            if (existingAttendance != null)
            {
                // 如果存在，询问是否覆盖
                var confirmDialog = new ContentDialog
                {
                    Title = "确认操作",
                    Content = "该员工当日已有考勤记录，是否覆盖？",
                    PrimaryButtonText = "覆盖",
                    CloseButtonText = "取消"
                };

                var result = await confirmDialog.ShowAsync();
                if (result != ContentDialogResult.Primary)
                {
                    return;
                }

                // 更新已有记录
                attendance.Id = existingAttendance.Id;
                bool success = await _attendanceService.UpdateAttendanceAsync(attendance);

                if (!success)
                {
                    var errorDialog = new ContentDialog()
                    {
                        Title = "错误",
                        Content = "更新考勤记录失败！",
                        PrimaryButtonText = "确定"
                    };
                    await errorDialog.ShowAsync();
                    return;
                }
            }
            else
            {
                // 添加新记录
                bool success = await _attendanceService.InsertAttendanceAsync(attendance);

                if (!success)
                {
                    var errorDialog = new ContentDialog()
                    {
                        Title = "错误",
                        Content = "添加考勤记录失败！",
                        PrimaryButtonText = "确定"
                    };
                    await errorDialog.ShowAsync();
                    return;
                }
            }

            _dialog.Hide(ContentDialogResult.Primary);
        }

        private void Clear()
        {
            EmployeeInput = "";
            SearchText = "";
            SearchResults = Array.Empty<string>();
            IsDropDownOpen = false;
            SelectedEmployee = null;
            AttendanceDate = DateTime.Today;
            Status = "正常";
            Notes = "";
        }
        
        private string _searchText = string.Empty;
        public string SearchText
        {
            get => _searchText;
            set => this.RaiseAndSetIfChanged(ref _searchText, value);
        }

        private void DialogOnClosed(ContentDialog sender, ContentDialogClosedEventArgs args)
        {
            _dialog.Closed -= DialogOnClosed;
            if (args.Result == ContentDialogResult.Primary)
            {
                var resultHint = new ContentDialog()
                {
                    Content = "考勤记录添加成功",
                    Title = "操作成功",
                    PrimaryButtonText = "好的"
                };
                _ = resultHint.ShowAsync();
            }
        }
    }
}
