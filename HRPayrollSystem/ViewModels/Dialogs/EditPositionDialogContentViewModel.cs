using System;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class EditPositionDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly PositionManagementService _positionManagementService;
    private readonly Position _originalPosition;

    private string _name = "";
    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }
    
    private string _description = "";
    public string Description
    {
        get => _description;
        set => this.RaiseAndSetIfChanged(ref _description, value);
    }

    public EditPositionDialogContentViewModel(ContentDialog dialog, PositionManagementService positionManagementService, Position position)
    {
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        _dialog = dialog;
        _positionManagementService = positionManagementService;
        _originalPosition = position;

        // 初始化数据
        Name = position.Name ?? "";
        Description = position.Description ?? "";

        dialog.Closed += DialogOnClosed;
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;
            args.Cancel = true;
            if (string.IsNullOrWhiteSpace(Name))
            {
                var resultHint = new ContentDialog()
                {
                    Content = "岗位名称不能为空",
                    Title = "添加岗位失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
                return;
            }
            var updatedPosition = new Position
            {
                Id = _originalPosition.Id,
                Name = Name,
                Description = Description
            };
            
            var succeeded = await _positionManagementService.UpdatePositionAsync(updatedPosition);
            if (!succeeded)
            {
                var resultHint = new ContentDialog()
                {
                    Content = "更新岗位失败，请检查输入",
                    Title = "操作失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
            else
            {
                _dialog.Hide();
            }
        };
    }

    private void DialogOnClosed(ContentDialog sender, ContentDialogClosedEventArgs args)
    {
        _dialog.Closed -= DialogOnClosed;
        if (args.Result == ContentDialogResult.Primary)
        {
            var resultHint = new ContentDialog()
            {
                Content = "岗位更新成功",
                Title = "操作成功",
                PrimaryButtonText = "好的",
                DefaultButton = ContentDialogButton.Primary
            };
            _ = resultHint.ShowAsync();
        }
    }

} 