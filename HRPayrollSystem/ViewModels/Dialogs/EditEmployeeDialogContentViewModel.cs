using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Runtime.InteropServices.JavaScript;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class EditEmployeeDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly EmployeeManagementService _employeeManagementService;
    private readonly Employee _originalEmployee;

    private bool _isResignationInfoVisible;
    public bool IsResignationInfoVisible
    {
        get => _isResignationInfoVisible;
        set => this.RaiseAndSetIfChanged(ref _isResignationInfoVisible, value);
    }

    private int _id;
    private string _employeeNumber = "";
    private string _name = "";
    private string _gender = "";
    private string _idCard = "";
    private DateTime? _birthday;
    private string _phone = "";
    private string _email = "";
    private string _address = "";
    private int? _departmentId;
    private int? _positionId;
    private DateTime? _hireDate;
    private DateTime? _probationEndDate;
    private string _status = "在职";
    private decimal _baseSalary;
    private DateTime? _resignationDate;
    private string _resignationReason = "";
    private string _departmentNameWithId = "";
    private string _positionNameWithId = "";

    public List<Department> AvailableDepartments { get; private set; } = new();
    public List<Position> AvailablePositions { get; private set; } = new();
    public ObservableCollection<string> DepartmentOptions { get; private set; } = new();
    public ObservableCollection<string> PositionOptions { get; private set; } = new();
    public List<string> GenderOptions { get; } = new() { "男", "女" };
    public List<string> StatusOptions { get; } = new() { "在职", "离职" };

    public EditEmployeeDialogContentViewModel(ContentDialog dialog, EmployeeManagementService employeeManagementService, Employee employee)
    {
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        _dialog = dialog;
        _employeeManagementService = employeeManagementService;
        _originalEmployee = employee;
        
        // 初始化数据
        Id = employee.Id;
        EmployeeNumber = employee.EmployeeNumber ?? "";
        Name = employee.Name ?? "";
        Gender = employee.Gender ?? "";
        IdCard = employee.IdCard ?? "";
        Birthday = employee.Birthday ?? DateTime.Today;
        Phone = employee.Phone ?? "";
        Email = employee.Email ?? "";
        Address = employee.Address ?? "";
        DepartmentId = employee.DepartmentId;
        PositionId = employee.PositionId;
        HireDate = employee.HireDate ?? DateTime.Today;
        ProbationEndDate = employee.ProbationEndDate ?? DateTime.Today.AddMonths(3);
        Status = employee.Status ?? "在职";
        BaseSalary = employee.BaseSalary;
        ResignationDate = employee.ResignationDate;
        ResignationReason = employee.ResignationReason ?? "";

        // 初始化离职信息可见性
        IsResignationInfoVisible = Status == "离职";

        _ = LoadOptionsAsync();
        
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;

            // 验证必填字段
            if (string.IsNullOrWhiteSpace(EmployeeNumber) || string.IsNullOrWhiteSpace(Name) || 
                DepartmentNameWithId == "无" || PositionNameWithId == "无" || 
                HireDate == null || ProbationEndDate == null || 
                string.IsNullOrWhiteSpace(Status))
            {
                var errorDialog = new ContentDialog
                {
                    Content = "员工编号、姓名、部门、职位、入职日期、试用期结束日期和状态为必填项，请填写完整",
                    Title = "验证失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = errorDialog.ShowAsync();
                return;
            }

            // 如果选择了离职状态，离职时间为必填项
            if (Status == "离职" && ResignationDate == null)
            {
                var errorDialog = new ContentDialog
                {
                    Content = "选择离职状态时，离职时间为必填项，请填写完整",
                    Title = "验证失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = errorDialog.ShowAsync();
                return;
            }

            UpdateDepartmentIdFromName();
            UpdatePositionIdFromName();

            var updatedEmployee = new Employee
            {
                Id = Id,
                EmployeeNumber = EmployeeNumber,
                Name = Name,
                Gender = Gender,
                IdCard = IdCard,
                Birthday = Birthday,
                Phone = Phone,
                Email = Email,
                Address = Address,
                DepartmentId = DepartmentId,
                PositionId = PositionId,
                HireDate = HireDate,
                ProbationEndDate = ProbationEndDate,
                Status = Status,
                BaseSalary = BaseSalary,
                ResignationDate = ResignationDate,
                ResignationReason = ResignationReason,
                CreatedAt = _originalEmployee.CreatedAt,
                UpdatedAt = DateTime.Now
            };

            var succeeded = await _employeeManagementService.UpdateEmployeeAsync(updatedEmployee);
            if (!succeeded)
            {
                var resultHint = new ContentDialog
                {
                    Content = "更新员工信息失败",
                    Title = "更新失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
            else
            {
                _dialog.Hide(ContentDialogResult.Primary);
                var successHint = new ContentDialog
                {
                    Content = "员工信息更新成功",
                    Title = "操作成功",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = successHint.ShowAsync();
            }
        };

        dialog.SecondaryButtonClick += (sender, args) =>
        {
            // 重置为原始值
            Id = _originalEmployee.Id;
            EmployeeNumber = _originalEmployee.EmployeeNumber ?? "";
            Name = _originalEmployee.Name ?? "";
            Gender = _originalEmployee.Gender ?? "";
            IdCard = _originalEmployee.IdCard ?? "";
            Birthday = _originalEmployee.Birthday ?? DateTime.Today;
            Phone = _originalEmployee.Phone ?? "";
            Email = _originalEmployee.Email ?? "";
            Address = _originalEmployee.Address ?? "";
            DepartmentId = _originalEmployee.DepartmentId;
            PositionId = _originalEmployee.PositionId;
            HireDate = _originalEmployee.HireDate ?? DateTime.Today;
            ProbationEndDate = _originalEmployee.ProbationEndDate ?? DateTime.Today.AddMonths(3);
            Status = _originalEmployee.Status ?? "在职";
            BaseSalary = _originalEmployee.BaseSalary;
            ResignationDate = _originalEmployee.ResignationDate;
            ResignationReason = _originalEmployee.ResignationReason ?? "";
            args.Cancel = true;
        };
    }

    private async Task LoadOptionsAsync()
    {
        await LoadDepartmentOptionsAsync();
        await LoadPositionOptionsAsync();
    }

    private async Task LoadDepartmentOptionsAsync()
    {
        try
        {
            // 先清空旧数据，以防重复加载
            DepartmentOptions.Clear();
            var available = await _employeeManagementService.GetAllDepartmentsAsync();
            this.AvailableDepartments = available;

            // 添加顶级选项
            DepartmentOptions.Add("无");

            // 遍历并添加其他选项
            var optionsToAdd = available
                .Select(d => $"{d.Name} (ID: {d.Id})");

            foreach (var option in optionsToAdd)
            {
                DepartmentOptions.Add(option);
            }

            // 设置当前选中的部门
            if (DepartmentId == null)
            {
                DepartmentNameWithId = "无";
            }
            else
            {
                var currentDepartment = available.FirstOrDefault(d => d.Id == DepartmentId);
                if (currentDepartment != null)
                {
                    DepartmentNameWithId = $"{currentDepartment.Name} (ID: {currentDepartment.Id})";
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载部门选项时发生错误: {ex.Message}");
        }
    }

    private async Task LoadPositionOptionsAsync()
    {
        try
        {
            // 先清空旧数据，以防重复加载
            PositionOptions.Clear();
            var available = await _employeeManagementService.GetAllPositionsAsync();
            this.AvailablePositions = available;

            // 添加顶级选项
            PositionOptions.Add("无");

            // 遍历并添加其他选项
            var optionsToAdd = available
                .Select(p => $"{p.Name} (ID: {p.Id})");

            foreach (var option in optionsToAdd)
            {
                PositionOptions.Add(option);
            }

            // 设置当前选中的职位
            if (PositionId == null)
            {
                PositionNameWithId = "无";
            }
            else
            {
                var currentPosition = available.FirstOrDefault(p => p.Id == PositionId);
                if (currentPosition != null)
                {
                    PositionNameWithId = $"{currentPosition.Name} (ID: {currentPosition.Id})";
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载职位选项时发生错误: {ex.Message}");
        }
    }

    public void UpdateDepartmentIdFromName()
    {
        if (DepartmentNameWithId == "无")
        {
            DepartmentId = null;
            return;
        }

        var match = AvailableDepartments.FirstOrDefault(d => $"{d.Name} (ID: {d.Id})" == DepartmentNameWithId);
        if (match != null)
        {
            DepartmentId = match.Id;
        }
    }

    public void UpdatePositionIdFromName()
    {
        if (PositionNameWithId == "无")
        {
            PositionId = null;
            return;
        }

        var match = AvailablePositions.FirstOrDefault(p => $"{p.Name} (ID: {p.Id})" == PositionNameWithId);
        if (match != null)
        {
            PositionId = match.Id;
        }
    }

    public int Id
    {
        get => _id;
        set => this.RaiseAndSetIfChanged(ref _id, value);
    }

    public string EmployeeNumber
    {
        get => _employeeNumber;
        set => this.RaiseAndSetIfChanged(ref _employeeNumber, value);
    }

    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }

    public string Gender
    {
        get => _gender;
        set => this.RaiseAndSetIfChanged(ref _gender, value);
    }

    public string IdCard
    {
        get => _idCard;
        set => this.RaiseAndSetIfChanged(ref _idCard, value);
    }

    public DateTime? Birthday
    {
        get => _birthday;
        set => this.RaiseAndSetIfChanged(ref _birthday, value);
    }

    public string Phone
    {
        get => _phone;
        set => this.RaiseAndSetIfChanged(ref _phone, value);
    }

    public string Email
    {
        get => _email;
        set => this.RaiseAndSetIfChanged(ref _email, value);
    }

    public string Address
    {
        get => _address;
        set => this.RaiseAndSetIfChanged(ref _address, value);
    }

    public int? DepartmentId
    {
        get => _departmentId;
        set => this.RaiseAndSetIfChanged(ref _departmentId, value);
    }

    public int? PositionId
    {
        get => _positionId;
        set => this.RaiseAndSetIfChanged(ref _positionId, value);
    }

    public DateTime? HireDate
    {
        get => _hireDate;
        set => this.RaiseAndSetIfChanged(ref _hireDate, value);
    }

    public DateTime? ProbationEndDate
    {
        get => _probationEndDate;
        set => this.RaiseAndSetIfChanged(ref _probationEndDate, value);
    }

    public string Status
    {
        get => _status;
        set 
        {
            this.RaiseAndSetIfChanged(ref _status, value);
            // 当状态改变时，更新离职信息的可见性
            IsResignationInfoVisible = value == "离职";
        }
    }

    public decimal BaseSalary
    {
        get => _baseSalary;
        set => this.RaiseAndSetIfChanged(ref _baseSalary, value);
    }

    public DateTime? ResignationDate
    {
        get => _resignationDate;
        set => this.RaiseAndSetIfChanged(ref _resignationDate, value);
    }

    public string ResignationReason
    {
        get => _resignationReason;
        set => this.RaiseAndSetIfChanged(ref _resignationReason, value);
    }

    public string DepartmentNameWithId
    {
        get => _departmentNameWithId;
        set => this.RaiseAndSetIfChanged(ref _departmentNameWithId, value);
    }

    public string PositionNameWithId
    {
        get => _positionNameWithId;
        set => this.RaiseAndSetIfChanged(ref _positionNameWithId, value);
    }
} 