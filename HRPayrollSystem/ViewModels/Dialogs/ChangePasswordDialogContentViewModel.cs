using System;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels.Dialogs;

public class ChangePasswordDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly UserManagementService _userManagementService;
    private readonly User _originalUser;

    private int _id;
    private string _employeeNumber = "";
    private string _role = "";
    private string _newPassword = "";
    private string _confirmPassword = "";

    public ChangePasswordDialogContentViewModel(ContentDialog dialog, UserManagementService userManagementService, User user)
    {
        if (dialog is null)
        {
            throw new ArgumentNullException(nameof(dialog));
        }

        _dialog = dialog;
        _userManagementService = userManagementService;
        _originalUser = user;
        
        // 初始化数据
        Id = user.Id;
        EmployeeNumber = user.EmployeeNumber ?? "";
        Role = user.Role ?? "";

        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;

            // 验证密码
            if (string.IsNullOrWhiteSpace(NewPassword))
            {
                var errorDialog = new ContentDialog
                {
                    Content = "新密码不能为空",
                    Title = "验证失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = errorDialog.ShowAsync();
                return;
            }

            if (NewPassword != ConfirmPassword)
            {
                var errorDialog = new ContentDialog
                {
                    Content = "两次输入的密码不一致，请重新输入",
                    Title = "验证失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = errorDialog.ShowAsync();
                return;
            }

            if (NewPassword.Length < 6)
            {
                var errorDialog = new ContentDialog
                {
                    Content = "密码长度不能少于6位",
                    Title = "验证失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = errorDialog.ShowAsync();
                return;
            }

            var updatedUser = new User
            {
                Id = Id,
                EmployeeNumber = EmployeeNumber,
                Role = Role,
                PasswordHash = _userManagementService.HashPassword(NewPassword),
                CreatedAt = _originalUser.CreatedAt,
                UpdatedAt = DateTime.Now
            };

            var succeeded = await _userManagementService.UpdateUserAsync(updatedUser);
            if (!succeeded)
            {
                var resultHint = new ContentDialog
                {
                    Content = "修改密码失败",
                    Title = "修改失败",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = resultHint.ShowAsync();
            }
            else
            {
                _dialog.Hide(ContentDialogResult.Primary);
                var successHint = new ContentDialog
                {
                    Content = "密码修改成功",
                    Title = "操作成功",
                    PrimaryButtonText = "好的",
                    DefaultButton = ContentDialogButton.Primary
                };
                _ = successHint.ShowAsync();
            }
        };

        dialog.SecondaryButtonClick += (sender, args) =>
        {
            // 重置为原始值
            NewPassword = "";
            ConfirmPassword = "";
            args.Cancel = true;
        };
    }

    public int Id
    {
        get => _id;
        set => this.RaiseAndSetIfChanged(ref _id, value);
    }

    public string EmployeeNumber
    {
        get => _employeeNumber;
        set => this.RaiseAndSetIfChanged(ref _employeeNumber, value);
    }

    public string Role
    {
        get => _role;
        set => this.RaiseAndSetIfChanged(ref _role, value);
    }

    public string NewPassword
    {
        get => _newPassword;
        set => this.RaiseAndSetIfChanged(ref _newPassword, value);
    }

    public string ConfirmPassword
    {
        get => _confirmPassword;
        set => this.RaiseAndSetIfChanged(ref _confirmPassword, value);
    }
}
