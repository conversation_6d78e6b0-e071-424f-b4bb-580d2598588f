using System;
using System.Collections.Generic;
using System.Reactive;
using System.Threading.Tasks;
using System.Windows.Input;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;
using System.Linq;
using System.Reactive.Linq;

namespace HRPayrollSystem.ViewModels.Dialogs;

public partial class AddRewardPunishmentDialogContentViewModel : ViewModelBase
{
    private readonly ContentDialog _dialog;
    private readonly RewardPunishmentManagementService _rewardPunishmentManagementService;
    private readonly UserManagementService _userManagementService;

    private string _employeeInput = "";
    public string EmployeeInput
    {
        get => _employeeInput;
        set => this.RaiseAndSetIfChanged(ref _employeeInput, value);
    }

    private IEnumerable<string> _searchResults = Array.Empty<string>();
    public IEnumerable<string> SearchResults
    {
        get => _searchResults;
        set => this.RaiseAndSetIfChanged(ref _searchResults, value);
    }
    
    private string _searchText = string.Empty;
    public string SearchText
    {
        get => _searchText;
        set
        {
            Console.WriteLine($"[AddRewardPunishment] SearchText changing from '{_searchText}' to '{value}'");
            this.RaiseAndSetIfChanged(ref _searchText, value);
        }
    }
    
    private bool _isDropDownOpen;
    public bool IsDropDownOpen
    {
        get => _isDropDownOpen;
        set => this.RaiseAndSetIfChanged(ref _isDropDownOpen, value);
    }

    private string _type = "奖励";
    public string Type
    {
        get => _type;
        set => this.RaiseAndSetIfChanged(ref _type, value);
    }

    private decimal _amount;
    public decimal Amount
    {
        get => _amount;
        set => this.RaiseAndSetIfChanged(ref _amount, value);
    }

    private string? _reason;
    public string? Reason
    {
        get => _reason;
        set => this.RaiseAndSetIfChanged(ref _reason, value);
    }

    private DateTime _recordDate = DateTime.Today;
    public DateTime RecordDate
    {
        get => _recordDate;
        set => this.RaiseAndSetIfChanged(ref _recordDate, value);
    }

    public List<string> TypeOptions { get; } = new() { "奖励", "处罚" };

    public AddRewardPunishmentDialogContentViewModel(ContentDialog dialog, RewardPunishmentManagementService rewardPunishmentManagementService, UserManagementService userManagementService)
    {
        _dialog = dialog;
        _rewardPunishmentManagementService = rewardPunishmentManagementService;
        _userManagementService = userManagementService;

        // 设置搜索功能
        Console.WriteLine("[AddRewardPunishment] Setting up search functionality");
        this.WhenAnyValue(x => x.SearchText)
            .Do(txt => Console.WriteLine($"[AddRewardPunishment] WhenAnyValue triggered with: '{txt}'"))
            .Throttle(TimeSpan.FromMilliseconds(300), RxApp.TaskpoolScheduler)
            .Do(txt => Console.WriteLine($"[AddRewardPunishment] After Throttle: '{txt}'"))
            .Where(txt => !string.IsNullOrWhiteSpace(txt))
            .Do(txt => Console.WriteLine($"[AddRewardPunishment] After Where filter: '{txt}'"))
            .DistinctUntilChanged()
            .Do(txt => Console.WriteLine($"[AddRewardPunishment] After DistinctUntilChanged: '{txt}', calling search service..."))
            .SelectMany(_userManagementService.SearchTop10EmployeeNumbersAndNamesAsync)   // 并发时只保留最后一次
            .ObserveOn(RxApp.MainThreadScheduler)
            .Do(list => Console.WriteLine($"[AddRewardPunishment] ObserveOn MainThread: Received {list?.Count() ?? 0} results"))
            .Subscribe(list =>
            {
                Console.WriteLine($"[AddRewardPunishment] Subscribe: Setting SearchResults and IsDropDownOpen");
                SearchResults = list;
                IsDropDownOpen = list.Any();   // 有结果才打开
                Console.WriteLine($"[AddRewardPunishment] Subscribe: IsDropDownOpen set to {list.Any()}");
            });

        // 设置对话框按钮事件
        dialog.PrimaryButtonClick += async (sender, args) =>
        {
            args.Cancel = true;
            await SaveAsync();
        };

        dialog.SecondaryButtonClick += (sender, args) =>
        {
            args.Cancel = true;
            Clear();
        };

        dialog.Closed += DialogOnClosed;
    }

    private void DialogOnClosed(ContentDialog sender, ContentDialogClosedEventArgs args)
    {
        _dialog.Closed -= DialogOnClosed;
        if (args.Result == ContentDialogResult.Primary)
        {
            var resultHint = new ContentDialog()
            {
                Content = "奖罚记录添加成功",
                Title = "操作成功",
                PrimaryButtonText = "好的"
            };
            _ = resultHint.ShowAsync();
        }
    }

    private async Task SaveAsync()
    {
        if (string.IsNullOrWhiteSpace(EmployeeInput))
        {
            var errorDialog = new ContentDialog()
            {
                Title = "输入错误",
                Content = "请重新输入！",
                PrimaryButtonText = "确定"
            };
            await errorDialog.ShowAsync();
            return;
        }

        // 验证员工是否存在
        var employee = await _rewardPunishmentManagementService.FindEmployeeByNumberAsync(EmployeeInput.Split(" ")[0]);
        if (employee == null)
        {
            var errorDialog = new ContentDialog()
            {
                Title = "该员工不存在！",
                Content = $"未找到员工：{EmployeeInput}",
                PrimaryButtonText = "确定"
            };
            await errorDialog.ShowAsync();
            return;
        }

        if (Amount <= 0)
        {
            var errorDialog = new ContentDialog()
            {
                Title = "错误",
                Content = "输出错误，请重新输入！",
                PrimaryButtonText = "确定"
            };
            await errorDialog.ShowAsync();
            return;
        }

        var rewardPunishment = new RewardPunishment
        {
            EmployeeId = employee.Id,
            Type = Type,
            Amount = Amount,
            Reason = Reason,
            RecordDate = RecordDate
        };

        var success = await _rewardPunishmentManagementService.InsertRewardPunishmentAsync(rewardPunishment);
        if (success)
        {
            _dialog.Hide(ContentDialogResult.Primary);
        }
        else
        {
            var errorDialog = new ContentDialog()
            {
                Title = "错误",
                Content = "输出错误，请重新输入！",
                PrimaryButtonText = "确定"
            };
            await errorDialog.ShowAsync();
        }
    }

    private void Clear()
    {
        EmployeeInput = "";
        SearchText = "";
        SearchResults = Array.Empty<string>();
        IsDropDownOpen = false;
        Type = "奖励";
        Amount = 0;
        Reason = "";
        RecordDate = DateTime.Today;
    }
} 