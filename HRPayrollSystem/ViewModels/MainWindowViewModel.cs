﻿using System.Collections.Generic;
using System.Linq;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels;

public class MainWindowViewModel : ViewModelBase
{
    private User? _currentUser;
    public User? CurrentUser
    {
        get => _currentUser;
        set => this.RaiseAndSetIfChanged(ref _currentUser, value);
    }

    private List<MenuItemData> _menuItems = new();
    public List<MenuItemData> MenuItems
    {
        get => _menuItems;
        set => this.RaiseAndSetIfChanged(ref _menuItems, value);
    }

    public MainWindowViewModel()
    {
        CurrentUser = CurrentUserService.CurrentUser;
        LoadMenuItems();
    }

    private void LoadMenuItems()
    {
        var allMenuItems = new List<MenuItemData>
        {
            new MenuItemData("我的工资", "Calculator", "HRPayrollSystem.Views.Pages.MyPayrollPage", new[] { "员工" }),
            new MenuItemData("数据分析", "ChartMultiple", "HRPayrollSystem.Views.Pages.DataAnalysisPage", new[] { "人事经理", "管理员" }),
            new MenuItemData("用户管理", "ContactInfo", "HRPayrollSystem.Views.Pages.UserManagementPage", new[] { "管理员" }),
            new MenuItemData("部门管理", "Link", "HRPayrollSystem.Views.Pages.DepartmentManagementPage", new[] { "人事经理" }),
            new MenuItemData("岗位管理", "Tag", "HRPayrollSystem.Views.Pages.PositionManagementPage", new[] { "人事经理" }),
            new MenuItemData("员工管理", "People", "HRPayrollSystem.Views.Pages.EmployeeManagementPage", new[] { "人事经理" }),
            new MenuItemData("奖罚管理", "Star", "HRPayrollSystem.Views.Pages.RewardPunishmentManagementPage", new[] { "人事经理" }),
            new MenuItemData("考勤管理", "Calendar", "HRPayrollSystem.Views.Pages.AttendanceManagementPage", new[] { "人事经理" }),
            new MenuItemData("工资管理", "DockBottom", "HRPayrollSystem.Views.Pages.PayrollManagementPage", new[] { "人事经理" })
        };

        // 调试信息
        System.Console.WriteLine($"当前用户ID: {CurrentUser?.Id}, 角色: {CurrentUser?.Role}");

        // 特例处理：如果用户ID是1，显示所有菜单项
        if (CurrentUser?.Id == 1)
        {
            System.Console.WriteLine("用户ID为1，显示所有菜单项");
            MenuItems = allMenuItems;
        }
        else
        {
            System.Console.WriteLine($"根据角色过滤菜单项，当前角色: {CurrentUser?.Role}");
            MenuItems = allMenuItems
                .Where(item => item.AllowedRoles.Contains(CurrentUser?.Role))
                .ToList();
        }

        System.Console.WriteLine($"最终菜单项数量: {MenuItems?.Count}");
    }
}

public class MenuItemData
{
    public string Title { get; }
    public string IconSource { get; }
    public string Tag { get; }
    public string[] AllowedRoles { get; }

    public MenuItemData(string title, string iconSource, string tag, string[] allowedRoles)
    {
        Title = title;
        IconSource = iconSource;
        Tag = tag;
        AllowedRoles = allowedRoles;
    }
}