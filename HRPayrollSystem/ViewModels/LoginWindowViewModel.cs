using System;
using System.Reactive;
using System.Reactive.Linq;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using HRPayrollSystem.Models;
using HRPayrollSystem.Services;
using ReactiveUI;

namespace HRPayrollSystem.ViewModels;

public class LoginWindowViewModel : ViewModelBase
{
    private readonly UserManagementService _userManagementService;
    
    private string _username = "";
    public string Username
    {
        get => _username;
        set => this.RaiseAndSetIfChanged(ref _username, value);
    }

    private string _password = "";
    public string Password
    {
        get => _password;
        set => this.RaiseAndSetIfChanged(ref _password, value);
    }

    private string _errorMessage = "";
    public string ErrorMessage
    {
        get => _errorMessage;
        set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
    }
    
    private readonly ObservableAsPropertyHelper<string> _loginButtonText;
    public string LoginButtonText => _loginButtonText.Value;

    private bool _isLoading = false;
    public bool IsLoading
    {
        get => _isLoading;
        set => this.RaiseAndSetIfChanged(ref _isLoading, value);
    }

    public ReactiveCommand<Unit, User?> LoginCommand { get; }
    public IObservable<User?> LoginResult => LoginCommand;

    public LoginWindowViewModel(UserManagementService userManagementService)
    {
        _userManagementService = userManagementService;
        
        _loginButtonText = this.WhenAnyValue(x => x.IsLoading)
            .Select(b => b ? "登录中…" : "登录")
            .ToProperty(this, nameof(LoginButtonText));
        
        var canLogin = this.WhenAnyValue(
            vm => vm.Username,
            vm => vm.Password,
            vm => vm.IsLoading,
            (username, password, isLoading) => 
                !string.IsNullOrWhiteSpace(username) && 
                !string.IsNullOrWhiteSpace(password) && 
                !isLoading);

        LoginCommand = ReactiveCommand.CreateFromTask(async () =>
        {
            IsLoading = true;
            ErrorMessage = "";
            
            try
            {
                var user = await _userManagementService.LoginAsync(Username, Password);
                if (user != null)
                {
                    Console.WriteLine($"用户 {user.EmployeeNumber} 登录成功，角色: {user.Role}");
                    return user;
                }
                else
                {
                    var dialog = new ContentDialog
                    {
                        Title = "登录失败",
                        Content = "用户名或密码错误，请重试。",
                        PrimaryButtonText = "确定",
                        DefaultButton = ContentDialogButton.Primary
                    };
                    await dialog.ShowAsync();
                    ErrorMessage = "用户名或密码错误";
                    return null;
                }
            }
            finally
            {
                IsLoading = false;
            }
        }, canLogin);
    }
}