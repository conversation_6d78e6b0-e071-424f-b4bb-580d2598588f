using System;
using System.Globalization;
using Avalonia.Data.Converters;

namespace HRPayrollSystem
{
    /// <summary>
    /// 将字符串和参数值进行比较的转换器，用于在XAML中进行条件比较
    /// </summary>
    public class StringEqualsConverter : IValueConverter
    {
        public static readonly StringEqualsConverter Instance = new StringEqualsConverter();

        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return false;

            return value.ToString() == parameter.ToString();
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 